import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    Switch,
    SafeAreaView,
    Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';

const Settings = () => {
    const { theme, toggleTheme } = useContext(ThemeContext);
    const { logout } = useContext(AuthContext);
    const router = useRouter();

    const [settings, setSettings] = useState({
        notifications: true,
        emailNotifications: true,
        pushNotifications: true,
        locationServices: true,
        biometricAuth: false,
        autoBackup: true,
        dataSync: true,
        darkMode: false,
    });

    const toggleSetting = (key) => {
        setSettings((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const handleLogout = () => {
        Alert.alert('Logout', 'Are you sure you want to logout?', [
            { text: 'Cancel', style: 'cancel' },
            {
                text: 'Logout',
                style: 'destructive',
                onPress: () => {
                    logout();
                    router.replace('/auth/Login');
                },
            },
        ]);
    };

    const handleDeleteAccount = () => {
        Alert.alert(
            'Delete Account',
            'This action cannot be undone. All your data will be permanently deleted.',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => {
                        // Handle account deletion
                        Alert.alert(
                            'Account Deleted',
                            'Your account has been deleted successfully.'
                        );
                    },
                },
            ]
        );
    };

    const settingsGroups = [
        {
            title: 'Account',
            items: [
                {
                    id: 'profile',
                    title: 'Edit Profile',
                    icon: 'person',
                    onPress: () => router.push('/Profile/EditProfile'),
                    showArrow: true,
                },
                {
                    id: 'security',
                    title: 'Security & Privacy',
                    icon: 'shield-checkmark',
                    onPress: () => router.push('/Settings/SecuritySettings'),
                    showArrow: true,
                },
                {
                    id: 'biometric',
                    title: 'Biometric Authentication',
                    icon: 'finger-print',
                    type: 'switch',
                    value: settings.biometricAuth,
                    onToggle: () => toggleSetting('biometricAuth'),
                },
            ],
        },
        {
            title: 'Notifications',
            items: [
                {
                    id: 'notifications',
                    title: 'Push Notifications',
                    icon: 'notifications',
                    type: 'switch',
                    value: settings.pushNotifications,
                    onToggle: () => toggleSetting('pushNotifications'),
                },
                {
                    id: 'email',
                    title: 'Email Notifications',
                    icon: 'mail',
                    type: 'switch',
                    value: settings.emailNotifications,
                    onToggle: () => toggleSetting('emailNotifications'),
                },
                {
                    id: 'notification-settings',
                    title: 'Notification Preferences',
                    icon: 'settings',
                    onPress: () =>
                        router.push('/Settings/NotificationSettings'),
                    showArrow: true,
                },
            ],
        },
        {
            title: 'App Preferences',
            items: [
                {
                    id: 'theme',
                    title: 'Dark Mode',
                    icon: 'moon',
                    type: 'switch',
                    value: settings.darkMode,
                    onToggle: () => {
                        toggleSetting('darkMode');
                        toggleTheme();
                    },
                },
                {
                    id: 'location',
                    title: 'Location Services',
                    icon: 'location',
                    type: 'switch',
                    value: settings.locationServices,
                    onToggle: () => toggleSetting('locationServices'),
                },
                {
                    id: 'language',
                    title: 'Language',
                    icon: 'language',
                    subtitle: 'English',
                    onPress: () => router.push('/Settings/LanguageSettings'),
                    showArrow: true,
                },
            ],
        },
        {
            title: 'Support',
            items: [
                {
                    id: 'help',
                    title: 'Help & Support',
                    icon: 'help-circle',
                    onPress: () => router.push('/Support/HelpSupport'),
                    showArrow: true,
                },
                {
                    id: 'feedback',
                    title: 'Send Feedback',
                    icon: 'chatbubble-ellipses',
                    onPress: () => router.push('/Support/HelpSupport'),
                    showArrow: true,
                },
                {
                    id: 'about',
                    title: 'About',
                    icon: 'information-circle',
                    onPress: () => router.push('/About/About'),
                    showArrow: true,
                },
            ],
        },
        {
            title: 'Account Actions',
            items: [
                {
                    id: 'logout',
                    title: 'Logout',
                    icon: 'log-out',
                    color: '#FF9800',
                    onPress: handleLogout,
                },
                {
                    id: 'delete',
                    title: 'Delete Account',
                    icon: 'trash',
                    color: '#F44336',
                    onPress: handleDeleteAccount,
                },
            ],
        },
    ];

    const renderSettingItem = (item) => (
        <TouchableOpacity
            key={item.id}
            style={[
                styles.settingItem,
                {
                    backgroundColor: theme.CARD,
                    borderBottomColor: theme.GRAY_LIGHT,
                },
            ]}
            onPress={item.onPress}
            disabled={item.type === 'switch'}
        >
            <View style={styles.settingLeft}>
                <View
                    style={[
                        styles.settingIcon,
                        {
                            backgroundColor:
                                (item.color || theme.PRIMARY) + '20',
                        },
                    ]}
                >
                    <Ionicons
                        name={item.icon}
                        size={16}
                        color={item.color || theme.PRIMARY}
                    />
                </View>
                <View style={styles.settingText}>
                    <Text
                        style={[
                            styles.settingTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        {item.title}
                    </Text>
                    {item.subtitle && (
                        <Text
                            style={[
                                styles.settingSubtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {item.subtitle}
                        </Text>
                    )}
                </View>
            </View>

            <View style={styles.settingRight}>
                {item.type === 'switch' ? (
                    <Switch
                        value={item.value}
                        onValueChange={item.onToggle}
                        trackColor={{
                            false: theme.TEXT_SECONDARY + '30',
                            true: theme.PRIMARY + '50',
                        }}
                        thumbColor={item.value ? theme.PRIMARY : '#f4f3f4'}
                    />
                ) : item.showArrow ? (
                    <Ionicons
                        name="chevron-forward"
                        size={20}
                        color={theme.TEXT_SECONDARY}
                    />
                ) : null}
            </View>
        </TouchableOpacity>
    );

    const renderSettingGroup = (group) => (
        <View key={group.title} style={styles.settingGroup}>
            <Text style={[styles.groupTitle, { color: theme.TEXT_PRIMARY }]}>
                {group.title}
            </Text>
            <View style={styles.groupItems}>
                {group.items.map(renderSettingItem)}
            </View>
        </View>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color="#fff" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Settings</Text>
                    <View style={{ width: 24 }} />
                </View>
            </LinearGradient>

            {/* Settings List */}
            <ScrollView
                style={styles.settingsList}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.settingsContent}
            >
                {settingsGroups.map(renderSettingGroup)}

                {/* App Version */}
                <View style={styles.versionContainer}>
                    <Text
                        style={[
                            styles.versionText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        BuildConnect v1.0.0
                    </Text>
                    <Text
                        style={[
                            styles.buildText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Build 2024.01.15
                    </Text>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 10,
        paddingBottom: 10,
        paddingHorizontal: 15,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    settingsList: {
        flex: 1,
    },
    settingsContent: {
        paddingHorizontal: 15,
        paddingBottom: 10,
    },
    settingGroup: {
        marginBottom: 15,
    },
    groupTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 12,
        marginLeft: 4,
    },
    groupItems: {
        borderRadius: 8,
        overflow: 'hidden',
    },
    settingItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 10,
    },
    settingLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    settingIcon: {
        width: 30,
        height: 30,
        borderRadius: 15,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    settingText: {
        flex: 1,
    },
    settingTitle: {
        fontSize: 14,
        fontWeight: '500',
    },
    settingSubtitle: {
        fontSize: 12,
        marginTop: 2,
    },
    settingRight: {
        marginLeft: 12,
    },
    versionContainer: {
        alignItems: 'center',
        paddingVertical: 20,
    },
    versionText: {
        fontSize: 14,
        fontWeight: '600',
    },
    buildText: {
        fontSize: 12,
        marginTop: 4,
    },
});

export default Settings;
