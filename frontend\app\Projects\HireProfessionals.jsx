import React, { useContext, useState, useRef, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    Image,
    Alert,
    Dimensions,
    Animated,
    Easing,
    ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchAllBrokers } from '../../api/broker/brokerApi';
import { fetchAllContractors } from '../../api/contractor/contractorApi';
import { sendServiceRequest } from '../../api/serviceRequests/serviceRequestApi';
import Toast from 'react-native-toast-message';

const { width, height } = Dimensions.get('window');

export default function HireProfessionals() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const { projectId, type } = useLocalSearchParams();
    const queryClient = useQueryClient();

    // Animation refs
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    const isBroker = type === 'broker';
    const title = isBroker ? 'Hire Broker' : 'Hire Contractor';

    const {
        data: professionalsData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: [isBroker ? 'allBrokers' : 'allContractors'],
        queryFn: isBroker ? fetchAllBrokers : fetchAllContractors,
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || `Failed to fetch ${type}s`,
            });
        },
    });

    const sendRequestMutation = useMutation({
        mutationFn: sendServiceRequest,
        onSuccess: () => {
            Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Service request sent successfully',
            });
            queryClient.invalidateQueries(['projectDetails', projectId]);
            router.back();
        },
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to send service request',
            });
        },
    });

    const professionals = professionalsData || [];

    // Initialize animations
    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const handleSendRequest = (professionalId) => {
        Alert.alert(
            'Hire Professional',
            `Are you sure you want to hire this ${type}?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Hire',
                    onPress: () => {
                        sendRequestMutation.mutate({
                            recipientId: professionalId,
                            recipientType: type,
                            projectId: projectId,
                        });
                    },
                },
            ]
        );
    };

    const ProfessionalCard = ({ professional }) => (
        <View style={[styles.card, { backgroundColor: theme.CARD }]}>
            <View style={styles.cardHeader}>
                <View style={styles.avatarContainer}>
                    {professional.image ? (
                        <Image
                            source={{ uri: professional.image }}
                            style={styles.avatar}
                        />
                    ) : (
                        <View
                            style={[
                                styles.avatarPlaceholder,
                                { backgroundColor: theme.PRIMARY + '20' },
                            ]}
                        >
                            <MaterialIcons
                                name={isBroker ? 'business' : 'build'}
                                size={24}
                                color={theme.PRIMARY}
                            />
                        </View>
                    )}
                </View>
                <View style={styles.professionalInfo}>
                    <Text
                        style={[
                            styles.professionalName,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                        numberOfLines={1}
                    >
                        {professional.name}
                    </Text>
                    <Text
                        style={[
                            styles.serviceArea,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                        numberOfLines={1}
                    >
                        {professional.serviceAreas?.join(', ') ||
                            'No service areas'}
                    </Text>
                    <View style={styles.ratingContainer}>
                        <MaterialIcons name="star" size={16} color="#FFD700" />
                        <Text
                            style={[
                                styles.rating,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {professional.ratings?.toFixed(1) || '0.0'}
                        </Text>
                    </View>
                </View>
            </View>

            <TouchableOpacity
                style={styles.hireButton}
                onPress={() => handleSendRequest(professional.id)}
                activeOpacity={0.8}
                disabled={sendRequestMutation.isLoading}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.gradient}
                />
                <Text style={[styles.hireButtonText, { color: theme.WHITE }]}>
                    {sendRequestMutation.isLoading ? 'Hiring...' : 'Hire'}
                </Text>
            </TouchableOpacity>
        </View>
    );

    const EmptyState = () => (
        <View style={styles.emptyContainer}>
            <MaterialIcons
                name={isBroker ? 'business' : 'build'}
                size={80}
                color={theme.TEXT_SECONDARY}
            />
            <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                No {isBroker ? 'Brokers' : 'Contractors'} Available
            </Text>
            <Text
                style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}
            >
                There are no verified {type}s available in your area at the
                moment.
            </Text>
        </View>
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Background Pattern */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            <SafeAreaView style={styles.safeArea}>
                {/* Header with Gradient */}
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    style={styles.header}
                >
                    <View style={styles.headerContent}>
                        <TouchableOpacity onPress={() => router.back()}>
                            <Ionicons
                                name="arrow-back"
                                size={24}
                                color="#fff"
                            />
                        </TouchableOpacity>
                        <Text style={styles.headerTitle}>{title}</Text>
                        <View style={{ width: 24 }} />
                    </View>
                </LinearGradient>

                <ScrollView
                    style={styles.scrollView}
                    showsVerticalScrollIndicator={false}
                >
                    <Animated.View
                        style={[
                            styles.content,
                            {
                                transform: [{ scale: scaleAnim }],
                                opacity: fadeAnim,
                            },
                        ]}
                    >
                        {isLoading ? (
                            <View style={styles.loadingContainer}>
                                <Text
                                    style={[
                                        styles.loadingText,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Loading {type}s...
                                </Text>
                            </View>
                        ) : professionals.length === 0 ? (
                            <EmptyState />
                        ) : (
                            <View style={styles.professionalsList}>
                                {professionals.map((professional) => (
                                    <ProfessionalCard
                                        key={professional.id}
                                        professional={professional}
                                    />
                                ))}
                            </View>
                        )}
                    </Animated.View>
                </ScrollView>
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    safeArea: {
        flex: 1,
    },
    header: {
        paddingTop: 20,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingTop: 20,
        paddingBottom: 100,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
    },
    loadingText: {
        fontSize: 16,
    },
    professionalsList: {
        gap: 16,
    },
    card: {
        borderRadius: 12,
        padding: 16,
        elevation: 2,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    avatarContainer: {
        marginRight: 12,
    },
    avatar: {
        width: 60,
        height: 60,
        borderRadius: 30,
    },
    avatarPlaceholder: {
        width: 60,
        height: 60,
        borderRadius: 30,
        alignItems: 'center',
        justifyContent: 'center',
    },
    professionalInfo: {
        flex: 1,
    },
    professionalName: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    serviceArea: {
        fontSize: 14,
        marginBottom: 4,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    rating: {
        fontSize: 12,
    },
    hireButton: {
        borderRadius: 8,
        overflow: 'hidden',
        elevation: 2,
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    hireButtonText: {
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
        paddingVertical: 12,
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    emptyTitle: {
        fontSize: 20,
        fontWeight: '600',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 14,
        textAlign: 'center',
        lineHeight: 20,
    },
});
