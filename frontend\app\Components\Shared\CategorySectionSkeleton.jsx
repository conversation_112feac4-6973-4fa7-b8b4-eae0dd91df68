import React, { useContext, useRef, useEffect } from 'react';
import { View, Animated, Dimensions } from 'react-native';
import { ThemeContext } from '../../../context/ThemeContext';

const { width } = Dimensions.get('window');
const cardWidth = (width - 48) / 3;

const SkeletonCard = () => {
    const { theme } = useContext(ThemeContext);
    const shimmerAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        const shimmerAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(shimmerAnim, {
                    toValue: 1,
                    duration: 1000,
                    useNativeDriver: true,
                }),
                Animated.timing(shimmerAnim, {
                    toValue: 0,
                    duration: 1000,
                    useNativeDriver: true,
                }),
            ])
        );
        shimmerAnimation.start();

        return () => shimmerAnimation.stop();
    }, [shimmerAnim]);

    const shimmerOpacity = shimmerAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0.3, 0.7],
    });

    return (
        <View
            style={{
                width: cardWidth,
                height: cardWidth * 1.4,
                borderRadius: 12,
                padding: 10,
                marginHorizontal: 4,
                marginBottom: 8,
                alignItems: 'center',
                justifyContent: 'flex-start',
                backgroundColor: theme.CARD,
                shadowColor: theme.PRIMARY,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.1,
                shadowRadius: 3.84,
                elevation: 5,
            }}
        >
            {/* Avatar Skeleton */}
            <View style={{ alignItems: 'center', marginBottom: 6 }}>
                <Animated.View
                    style={{
                        width: 45,
                        height: 45,
                        borderRadius: 22.5,
                        backgroundColor: theme.INPUT_BACKGROUND,
                        opacity: shimmerOpacity,
                    }}
                />
            </View>

            {/* Name Skeleton */}
            <Animated.View
                style={{
                    width: '80%',
                    height: 16,
                    borderRadius: 8,
                    backgroundColor: theme.INPUT_BACKGROUND,
                    marginBottom: 3,
                    opacity: shimmerOpacity,
                }}
            />

            {/* Service Area Skeleton */}
            <Animated.View
                style={{
                    width: '60%',
                    height: 13,
                    borderRadius: 6,
                    backgroundColor: theme.INPUT_BACKGROUND,
                    marginBottom: 6,
                    opacity: shimmerOpacity,
                }}
            />

            {/* Rating Skeleton */}
            <View
                style={{
                    alignItems: 'center',
                    flex: 1,
                    marginTop: 4,
                    justifyContent: 'flex-end',
                    paddingBottom: 2,
                }}
            >
                {/* Stars Skeleton */}
                <View
                    style={{
                        flexDirection: 'row',
                        marginBottom: 2,
                    }}
                >
                    {[...Array(5)].map((_, index) => (
                        <Animated.View
                            key={index}
                            style={{
                                width: 12,
                                height: 12,
                                borderRadius: 6,
                                backgroundColor: theme.INPUT_BACKGROUND,
                                marginHorizontal: 1,
                                opacity: shimmerOpacity,
                            }}
                        />
                    ))}
                </View>

                {/* Rating Text Skeleton */}
                <Animated.View
                    style={{
                        width: 25,
                        height: 10,
                        borderRadius: 5,
                        backgroundColor: theme.INPUT_BACKGROUND,
                        opacity: shimmerOpacity,
                    }}
                />
            </View>
        </View>
    );
};

export default function CategorySectionSkeleton({ title }) {
    const { theme } = useContext(ThemeContext);
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const shimmerAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        // Fade in animation
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
        }).start();

        // Title shimmer animation
        const titleShimmer = Animated.loop(
            Animated.sequence([
                Animated.timing(shimmerAnim, {
                    toValue: 1,
                    duration: 1200,
                    useNativeDriver: true,
                }),
                Animated.timing(shimmerAnim, {
                    toValue: 0,
                    duration: 1200,
                    useNativeDriver: true,
                }),
            ])
        );
        titleShimmer.start();

        return () => titleShimmer.stop();
    }, [fadeAnim, shimmerAnim]);

    const titleShimmerOpacity = shimmerAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0.4, 0.8],
    });

    return (
        <Animated.View
            style={{
                opacity: fadeAnim,
                marginTop: 1,
                marginHorizontal: -6,
                borderRadius: 10,
                marginVertical: 8,
                paddingVertical: 6,
                backgroundColor: theme.CARD + 'CC',
            }}
        >
            {/* Header Section */}
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingHorizontal: 16,
                    marginBottom: 12,
                }}
            >
                {/* Title Skeleton */}
                <Animated.View
                    style={{
                        width: title ? title.length * 8 : 120,
                        height: 20,
                        borderRadius: 10,
                        backgroundColor: theme.INPUT_BACKGROUND,
                        opacity: titleShimmerOpacity,
                    }}
                />

                {/* View All Button Skeleton */}
                <Animated.View
                    style={{
                        width: 80,
                        height: 28,
                        borderRadius: 999,
                        backgroundColor: theme.PRIMARY + '25',
                        opacity: titleShimmerOpacity,
                    }}
                />
            </View>

            {/* Cards Container */}
            <View
                style={{
                    flexDirection: 'row',
                    paddingHorizontal: 16,
                }}
            >
                {/* Render 3 skeleton cards */}
                {[...Array(3)].map((_, index) => (
                    <SkeletonCard key={index} />
                ))}
            </View>
        </Animated.View>
    );
}
