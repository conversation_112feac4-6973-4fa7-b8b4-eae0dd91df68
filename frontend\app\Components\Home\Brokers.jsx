import React from 'react';
import { Text } from 'react-native';
import { useRouter } from 'expo-router';
import CategorySection from '../Shared/CategorySection';
import CategorySectionSkeleton from '../Shared/CategorySectionSkeleton';
import { useQuery } from '@tanstack/react-query';
import { fetchAllBrokers } from '../../../api/broker/brokerApi';

export default function Brokers() {
    const router = useRouter();
    const {
        data: brokers,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['allBrokers'],
        queryFn: fetchAllBrokers,
    });

    const handleBrokerPress = (broker) => {
        router.push({
            pathname: '/Profile/BrokerProfile',
            params: { brokerId: broker.id },
        });
    };

    return isLoading ? (
        <CategorySectionSkeleton title="Featured Brokers" />
    ) : isError ? (
        <Text>Error: {error.message}</Text>
    ) : (
        <CategorySection
            title="Featured Brokers"
            role="broker"
            data={brokers || []}
            onItemPress={handleBrokerPress}
            viewAllRoute="/Broker/BrokerList"
        />
    );
}
