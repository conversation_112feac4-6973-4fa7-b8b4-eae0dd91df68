import { useRef, useEffect, useState, useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Animated,
    Easing,
    StatusBar,
    SafeAreaView,
} from 'react-native';
import Header from '../Components/Home/Header';
import Sites from '../Components/Home/Sites';
import Brokers from '../Components/Home/Brokers';
import Contractors from '../Components/Home/Contractors';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import QuickAccess from '../Components/Home/QuickAccess';
import { ThemeContext } from '../../context/ThemeContext';
import BannerSlider from '../Components/Home/BannerSlider';

const { height } = Dimensions.get('window');

export default function Home() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const [backgroundHeight, setBackgroundHeight] = useState(height * 0.38);
    const quickAccessRef = useRef(null);
    const scrollY = useRef(new Animated.Value(0)).current;

    // Animations
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(40)).current;
    const backgroundZoom = useRef(new Animated.Value(1)).current;

    // Parallax effect for background image
    const backgroundTranslateY = scrollY.interpolate({
        inputRange: [0, height * 0.38],
        outputRange: [0, -50],
        extrapolate: 'clamp',
    });

    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 700,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 700,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
            Animated.loop(
                Animated.sequence([
                    Animated.timing(backgroundZoom, {
                        toValue: 1.04,
                        duration: 4000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: true,
                    }),
                    Animated.timing(backgroundZoom, {
                        toValue: 1,
                        duration: 4000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: true,
                    }),
                ])
            ),
        ]).start();
    }, [fadeAnim, slideAnim, backgroundZoom]);

    // Calculate background height
    const handleQuickAccessLayout = (event) => {
        const { y, height: layoutHeight } = event.nativeEvent.layout;
        setBackgroundHeight(y + layoutHeight + 80);
    };

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />
            <View
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    minHeight: height * 0.3,
                    zIndex: 0,
                    opacity: 0.8,
                }}
            >
                <LinearGradient
                    colors={[
                        theme.BACKGROUND + '80',
                        theme.GRADIENT_PRIMARY + '80',
                    ]}
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                    }}
                    start={{ x: 0.6, y: 1.2 }}
                    end={{ x: 0.2, y: 0.8 }}
                />
            </View>
            <Header />
            <Animated.ScrollView
                style={[styles.scrollView]}
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
                onScroll={Animated.event(
                    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                    { useNativeDriver: true }
                )}
                scrollEventThrottle={16}
            >
                {/* Banner Card */}
                <BannerSlider />
                {/* Quick Access Section */}
                <Animated.View
                    ref={quickAccessRef}
                    style={[styles.quickAccessSection, { opacity: fadeAnim }]}
                    onLayout={handleQuickAccessLayout}
                >
                    <QuickAccess />
                </Animated.View>

                {/* Featured Sections */}
                <Sites />
                <Contractors />
                <Brokers />

                {/* Support CTA */}
                <Animated.View
                    style={[
                        styles.supportCard,
                        {
                            shadowColor: theme.PRIMARY,
                            backgroundColor: theme.CARD,
                        },
                        { opacity: fadeAnim },
                    ]}
                >
                    <Text
                        style={[styles.supportTitle, { color: theme.PRIMARY }]}
                    >
                        Need Help?
                    </Text>
                    <Text style={styles.supportSubtitle}>
                        Our team is here to assist with any issues or questions.
                    </Text>
                    <TouchableOpacity
                        style={styles.supportButton}
                        onPress={() => router.push('/Support/HelpSupport')}
                        activeOpacity={0.85}
                    >
                        <LinearGradient
                            colors={[theme.PRIMARY, theme.SECONDARY]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                            style={styles.supportButtonGradient}
                        >
                            <Text
                                style={[
                                    styles.supportButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Raise a Ticket
                            </Text>
                            <Ionicons
                                name="chevron-forward"
                                size={16}
                                color={theme.WHITE}
                                style={styles.supportButtonIcon}
                            />
                        </LinearGradient>
                    </TouchableOpacity>
                </Animated.View>
            </Animated.ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    scrollContent: {
        paddingBottom: 64,
    },
    bannerImage: {
        width: '100%',
        height: '100%',
    },
    bannerOverlay: {
        ...StyleSheet.absoluteFillObject,
    },
    bannerCard: {
        height: 180,
        marginHorizontal: 8,
        marginTop: 6,
        borderRadius: 12,
        overflow: 'hidden',
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
        elevation: 5,
    },
    bannerCardImage: {
        width: '100%',
        height: '100%',
        position: 'absolute',
    },
    bannerCardOverlay: {
        flex: 1,
        padding: 10,
        justifyContent: 'space-between',
    },
    bannerTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 2,
        marginTop: 20,
        textShadowColor: 'rgba(0,0,0,0.4)',
        textShadowOffset: { width: 0, height: 2 },
        textShadowRadius: 6,
    },
    bannerSubtitle: {
        fontSize: 10,
        fontStyle: 'italic',
        marginBottom: 16,
        opacity: 0.95,
    },
    bannerButton: {
        alignSelf: 'flex-end',
    },
    bannerButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    bannerButtonText: {
        fontSize: 12,
        fontWeight: '600',
    },
    quickAccessSection: {
        paddingHorizontal: 8,
    },
    supportCard: {
        marginHorizontal: 16,
        marginVertical: 28,
        padding: 24,
        borderRadius: 24,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.12,
        shadowRadius: 16,
        elevation: 6,
    },
    supportTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    supportSubtitle: {
        color: '#666',
        fontSize: 15,
        marginBottom: 20,
    },
    supportButton: {
        alignSelf: 'flex-start',
    },
    supportButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 14,
        paddingHorizontal: 24,
        paddingVertical: 12,
    },
    supportButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    supportButtonIcon: {
        marginLeft: 10,
    },
    cardContainer: {
        marginHorizontal: 16,
        marginBottom: 24,
    },
});
