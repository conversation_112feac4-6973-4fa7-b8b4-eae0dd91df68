import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    SafeAreaView,
    Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

const BrokerDashboard = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [selectedPeriod, setSelectedPeriod] = useState('month');

    // Mock broker data
    const brokerStats = {
        totalListings: 45,
        activeListings: 32,
        soldProperties: 13,
        totalEarnings: 285000,
        monthlyEarnings: 45000,
        commission: 2.5,
        rating: 4.8,
        reviews: 156,
        clients: 89,
        viewsThisMonth: 2340,
    };

    const recentActivities = [
        {
            id: 1,
            type: 'listing',
            title: 'New property listed',
            description: '3BHK Apartment in Whitefield',
            time: '2 hours ago',
            icon: 'home',
            color: '#4CAF50',
        },
        {
            id: 2,
            type: 'inquiry',
            title: 'New inquiry received',
            description: 'Client interested in Villa project',
            time: '4 hours ago',
            icon: 'chatbubble',
            color: '#2196F3',
        },
        {
            id: 3,
            type: 'sale',
            title: 'Property sold',
            description: 'Plot in Electronic City - ₹85 Lakh',
            time: '1 day ago',
            icon: 'checkmark-circle',
            color: '#FF9800',
        },
        {
            id: 4,
            type: 'review',
            title: 'New review received',
            description: '5-star rating from satisfied client',
            time: '2 days ago',
            icon: 'star',
            color: '#9C27B0',
        },
    ];

    const quickActions = [
        {
            id: 'add-listing',
            title: 'Add Listing',
            icon: 'add-circle',
            color: '#4CAF50',
            onPress: () => router.push('/Properties/SellLand'),
        },
        {
            id: 'manage-listings',
            title: 'Manage Listings',
            icon: 'list',
            color: '#2196F3',
            onPress: () => router.push('/Broker/ManageListings'),
        },
        {
            id: 'client-inquiries',
            title: 'Inquiries',
            icon: 'mail',
            color: '#FF9800',
            onPress: () => router.push('/Broker/ClientInquiries'),
        },
        {
            id: 'analytics',
            title: 'Analytics',
            icon: 'analytics',
            color: '#9C27B0',
            onPress: () => router.push('/Analytics/BrokerAnalytics'),
        },
    ];

    const topListings = [
        {
            id: 1,
            title: 'Luxury Villa in Whitefield',
            price: '₹2.5 Cr',
            views: 234,
            inquiries: 12,
            status: 'active',
        },
        {
            id: 2,
            title: 'Commercial Plot in Electronic City',
            price: '₹85 Lakh',
            views: 189,
            inquiries: 8,
            status: 'active',
        },
        {
            id: 3,
            title: '3BHK Apartment in HSR Layout',
            price: '₹1.8 Cr',
            views: 156,
            inquiries: 15,
            status: 'sold',
        },
    ];

    const periods = [
        { id: 'week', name: 'This Week' },
        { id: 'month', name: 'This Month' },
        { id: 'quarter', name: 'This Quarter' },
        { id: 'year', name: 'This Year' },
    ];

    const renderStatCard = (title, value, subtitle, icon, color) => (
        <View
            style={[
                styles.statCard,
                { backgroundColor: theme.CARD_BACKGROUND },
            ]}
        >
            <View style={styles.statHeader}>
                <View
                    style={[styles.statIcon, { backgroundColor: color + '20' }]}
                >
                    <Ionicons name={icon} size={24} color={color} />
                </View>
                <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
                    {value}
                </Text>
            </View>
            <Text style={[styles.statTitle, { color: theme.TEXT_SECONDARY }]}>
                {title}
            </Text>
            {subtitle && (
                <Text
                    style={[
                        styles.statSubtitle,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {subtitle}
                </Text>
            )}
        </View>
    );

    const renderActivity = (activity) => (
        <TouchableOpacity
            key={activity.id}
            style={[
                styles.activityCard,
                { backgroundColor: theme.CARD_BACKGROUND },
            ]}
        >
            <View
                style={[
                    styles.activityIcon,
                    { backgroundColor: activity.color + '20' },
                ]}
            >
                <Ionicons
                    name={activity.icon}
                    size={20}
                    color={activity.color}
                />
            </View>
            <View style={styles.activityContent}>
                <Text
                    style={[
                        styles.activityTitle,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                >
                    {activity.title}
                </Text>
                <Text
                    style={[
                        styles.activityDescription,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {activity.description}
                </Text>
                <Text
                    style={[
                        styles.activityTime,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {activity.time}
                </Text>
            </View>
        </TouchableOpacity>
    );

    const renderQuickAction = (action) => (
        <TouchableOpacity
            key={action.id}
            style={[
                styles.actionCard,
                { backgroundColor: theme.CARD_BACKGROUND },
            ]}
            onPress={action.onPress}
        >
            <View
                style={[
                    styles.actionIcon,
                    { backgroundColor: action.color + '20' },
                ]}
            >
                <Ionicons name={action.icon} size={28} color={action.color} />
            </View>
            <Text style={[styles.actionTitle, { color: theme.TEXT_PRIMARY }]}>
                {action.title}
            </Text>
        </TouchableOpacity>
    );

    const renderListing = (listing) => (
        <TouchableOpacity
            key={listing.id}
            style={[
                styles.listingCard,
                { backgroundColor: theme.CARD_BACKGROUND },
            ]}
            onPress={() =>
                router.push(`/Properties/PropertyDetails?id=${listing.id}`)
            }
        >
            <View style={styles.listingHeader}>
                <Text
                    style={[styles.listingTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    {listing.title}
                </Text>
                <View
                    style={[
                        styles.statusBadge,
                        {
                            backgroundColor:
                                listing.status === 'active'
                                    ? '#4CAF50'
                                    : '#FF9800',
                        },
                    ]}
                >
                    <Text style={styles.statusText}>{listing.status}</Text>
                </View>
            </View>
            <Text style={[styles.listingPrice, { color: theme.PRIMARY }]}>
                {listing.price}
            </Text>
            <View style={styles.listingStats}>
                <View style={styles.statItem}>
                    <Ionicons
                        name="eye"
                        size={16}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.statText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {listing.views} views
                    </Text>
                </View>
                <View style={styles.statItem}>
                    <Ionicons
                        name="mail"
                        size={16}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.statText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {listing.inquiries} inquiries
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color="#fff" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Broker Dashboard</Text>
                    <TouchableOpacity
                        onPress={() => router.push('/Broker/BrokerProfile')}
                    >
                        <Ionicons name="person-circle" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>
            </LinearGradient>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
            >
                {/* Period Selector */}
                <View style={styles.periodSection}>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={styles.periodContainer}
                    >
                        {periods.map((period) => (
                            <TouchableOpacity
                                key={period.id}
                                style={[
                                    styles.periodButton,
                                    {
                                        backgroundColor:
                                            selectedPeriod === period.id
                                                ? theme.PRIMARY
                                                : theme.CARD_BACKGROUND,
                                    },
                                ]}
                                onPress={() => setSelectedPeriod(period.id)}
                            >
                                <Text
                                    style={[
                                        styles.periodText,
                                        {
                                            color:
                                                selectedPeriod === period.id
                                                    ? '#fff'
                                                    : theme.TEXT_SECONDARY,
                                        },
                                    ]}
                                >
                                    {period.name}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                </View>

                {/* Stats Overview */}
                <View style={styles.statsSection}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Overview
                    </Text>
                    <View style={styles.statsGrid}>
                        {renderStatCard(
                            'Total Listings',
                            brokerStats.totalListings,
                            `${brokerStats.activeListings} active`,
                            'home',
                            '#4CAF50'
                        )}
                        {renderStatCard(
                            'Properties Sold',
                            brokerStats.soldProperties,
                            'this month',
                            'checkmark-circle',
                            '#2196F3'
                        )}
                        {renderStatCard(
                            'Total Earnings',
                            `₹${(brokerStats.totalEarnings / 1000).toFixed(0)}K`,
                            `₹${(brokerStats.monthlyEarnings / 1000).toFixed(0)}K this month`,
                            'wallet',
                            '#FF9800'
                        )}
                        {renderStatCard(
                            'Rating',
                            brokerStats.rating,
                            `${brokerStats.reviews} reviews`,
                            'star',
                            '#9C27B0'
                        )}
                    </View>
                </View>

                {/* Quick Actions */}
                <View style={styles.actionsSection}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Quick Actions
                    </Text>
                    <View style={styles.actionsGrid}>
                        {quickActions.map(renderQuickAction)}
                    </View>
                </View>

                {/* Top Listings */}
                <View style={styles.listingsSection}>
                    <View style={styles.sectionHeader}>
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Top Listings
                        </Text>
                        <TouchableOpacity
                            onPress={() => router.push('/Broker/AllListings')}
                        >
                            <Text
                                style={[
                                    styles.viewAllText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                View All
                            </Text>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.listingsList}>
                        {topListings.map(renderListing)}
                    </View>
                </View>

                {/* Recent Activities */}
                <View style={styles.activitiesSection}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Recent Activities
                    </Text>
                    <View style={styles.activitiesList}>
                        {recentActivities.map(renderActivity)}
                    </View>
                </View>

                <View style={{ height: 40 }} />
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 20,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    content: {
        flex: 1,
    },
    periodSection: {
        paddingVertical: 16,
    },
    periodContainer: {
        paddingHorizontal: 20,
    },
    periodButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        marginRight: 10,
    },
    periodText: {
        fontSize: 14,
        fontWeight: '600',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
        paddingHorizontal: 20,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        marginBottom: 16,
    },
    viewAllText: {
        fontSize: 14,
        fontWeight: '600',
    },
    statsSection: {
        marginBottom: 24,
    },
    statsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        paddingHorizontal: 20,
        justifyContent: 'space-between',
    },
    statCard: {
        width: (width - 60) / 2,
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    statHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    statIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    statValue: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    statTitle: {
        fontSize: 14,
        marginBottom: 4,
    },
    statSubtitle: {
        fontSize: 12,
    },
    actionsSection: {
        marginBottom: 24,
    },
    actionsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        paddingHorizontal: 20,
        justifyContent: 'space-between',
    },
    actionCard: {
        width: (width - 60) / 2,
        padding: 16,
        borderRadius: 12,
        alignItems: 'center',
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    actionIcon: {
        width: 56,
        height: 56,
        borderRadius: 28,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
    },
    actionTitle: {
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
    },
    listingsSection: {
        marginBottom: 24,
    },
    listingsList: {
        paddingHorizontal: 20,
    },
    listingCard: {
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    listingHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    listingTitle: {
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: '#fff',
        textTransform: 'capitalize',
    },
    listingPrice: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    listingStats: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    statItem: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statText: {
        fontSize: 12,
        marginLeft: 4,
    },
    activitiesSection: {
        marginBottom: 24,
    },
    activitiesList: {
        paddingHorizontal: 20,
    },
    activityCard: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    activityIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    activityContent: {
        flex: 1,
    },
    activityTitle: {
        fontSize: 14,
        fontWeight: '600',
        marginBottom: 4,
    },
    activityDescription: {
        fontSize: 12,
        marginBottom: 4,
    },
    activityTime: {
        fontSize: 11,
    },
});

export default BrokerDashboard;
