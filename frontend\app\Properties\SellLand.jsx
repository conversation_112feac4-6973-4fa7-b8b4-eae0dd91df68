import React, { useState, useContext, useCallback } from 'react';
import {
    View,
    Text,
    ScrollView,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Alert,
    Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Formik } from 'formik';
import * as Yup from 'yup';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as Location from 'expo-location';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import { createLandListing, getLandCategories } from '../../api/land/landApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';
import MapView, { Marker } from 'react-native-maps';

const validationSchema = Yup.object().shape({
    title: Yup.string()
        .required('Title is required')
        .min(10, 'Title must be at least 10 characters'),
    description: Yup.string()
        .required('Description is required')
        .min(50, 'Description must be at least 50 characters'),
    price: Yup.number()
        .required('Price is required')
        .positive('Price must be positive'),
    area: Yup.number()
        .required('Area is required')
        .positive('Area must be positive'),
    areaUnit: Yup.string().required('Area unit is required'),
    landType: Yup.string().required('Land type is required'),
    ownershipType: Yup.string().required('Ownership type is required'),
    location: Yup.string().required('Location is required'),
});

const SellLand = () => {
    const { theme } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();

    const [selectedImages, setSelectedImages] = useState([]);
    const [selectedDocuments, setSelectedDocuments] = useState([]);
    const [selectedLocation, setSelectedLocation] = useState(null);

    // Fetch land categories
    const { data: categories = [] } = useQuery({
        queryKey: ['landCategories'],
        queryFn: getLandCategories,
    });

    // Create land listing mutation
    const createListingMutation = useMutation({
        mutationFn: createLandListing,
        onSuccess: (data) => {
            showToast(
                'success',
                'Success',
                'Land listing created successfully'
            );
            router.push({
                pathname: '/Properties/LandDetails',
                params: { landId: data.id },
            });
        },
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.message || 'Failed to create listing'
            );
        },
    });

    const handleImagePicker = useCallback(async () => {
        try {
            const { status } =
                await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (status !== 'granted') {
                showToast(
                    'error',
                    'Permission Required',
                    'Camera roll permission is required'
                );
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images'],
                allowsMultipleSelection: true,
                quality: 0.8,
                aspect: [16, 9],
            });

            if (!result.canceled) {
                setSelectedImages((prev) => [...prev, ...result.assets]);
            }
        } catch (error) {
            showToast('error', 'Error', 'Failed to pick images');
        }
    }, []);

    const handleDocumentPicker = useCallback(async () => {
        try {
            const result = await DocumentPicker.getDocumentAsync({
                type: ['application/pdf', 'image/*'],
                multiple: true,
            });

            if (!result.canceled) {
                setSelectedDocuments((prev) => [...prev, ...result.assets]);
            }
        } catch (error) {
            showToast('error', 'Error', 'Failed to pick documents');
        }
    }, []);

    const handleLocationPicker = useCallback(async () => {
        try {
            const { status } =
                await Location.requestForegroundPermissionsAsync();
            if (status !== 'granted') {
                showToast(
                    'error',
                    'Permission Required',
                    'Location permission is required'
                );
                return;
            }

            const location = await Location.getCurrentPositionAsync({});
            setSelectedLocation({
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
            });
        } catch (error) {
            showToast('error', 'Error', 'Failed to get current location');
        }
    }, []);

    const removeImage = useCallback((index) => {
        setSelectedImages((prev) => prev.filter((_, i) => i !== index));
    }, []);

    const removeDocument = useCallback((index) => {
        setSelectedDocuments((prev) => prev.filter((_, i) => i !== index));
    }, []);

    const handleSubmit = useCallback(
        async (values) => {
            if (selectedImages.length === 0) {
                showToast(
                    'error',
                    'Images Required',
                    'Please add at least one image'
                );
                return;
            }

            if (selectedDocuments.length === 0) {
                showToast(
                    'error',
                    'Documents Required',
                    'Please add property documents'
                );
                return;
            }

            if (!selectedLocation) {
                showToast(
                    'error',
                    'Location Required',
                    'Please select property location'
                );
                return;
            }

            const landData = {
                ...values,
                images: selectedImages,
                documents: selectedDocuments,
                coordinates: selectedLocation,
                sellerId: user.id,
            };

            createListingMutation.mutate(landData);
        },
        [
            selectedImages,
            selectedDocuments,
            selectedLocation,
            user,
            createListingMutation,
        ]
    );

    const renderImageSection = () => (
        <View
            style={[styles.section, { backgroundColor: theme.CARD_BACKGROUND }]}
        >
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                Property Images *
            </Text>

            <TouchableOpacity
                style={[styles.addButton, { borderColor: theme.PRIMARY }]}
                onPress={handleImagePicker}
            >
                <Ionicons name="camera" size={24} color={theme.PRIMARY} />
                <Text style={[styles.addButtonText, { color: theme.PRIMARY }]}>
                    Add Images
                </Text>
            </TouchableOpacity>

            {selectedImages.length > 0 && (
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.imageList}
                >
                    {selectedImages.map((image, index) => (
                        <View key={index} style={styles.imageItem}>
                            <Image
                                source={{ uri: image.uri }}
                                style={styles.selectedImage}
                            />
                            <TouchableOpacity
                                style={styles.removeButton}
                                onPress={() => removeImage(index)}
                            >
                                <Ionicons
                                    name="close-circle"
                                    size={20}
                                    color="#F44336"
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
            )}
        </View>
    );

    const renderDocumentSection = () => (
        <View
            style={[styles.section, { backgroundColor: theme.CARD_BACKGROUND }]}
        >
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                Property Documents *
            </Text>

            <TouchableOpacity
                style={[styles.addButton, { borderColor: theme.PRIMARY }]}
                onPress={handleDocumentPicker}
            >
                <Ionicons name="document" size={24} color={theme.PRIMARY} />
                <Text style={[styles.addButtonText, { color: theme.PRIMARY }]}>
                    Add Documents
                </Text>
            </TouchableOpacity>

            {selectedDocuments.length > 0 && (
                <View style={styles.documentList}>
                    {selectedDocuments.map((doc, index) => (
                        <View key={index} style={styles.documentItem}>
                            <Ionicons
                                name="document-text"
                                size={20}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.documentName,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                numberOfLines={1}
                            >
                                {doc.name}
                            </Text>
                            <TouchableOpacity
                                onPress={() => removeDocument(index)}
                            >
                                <Ionicons
                                    name="close-circle"
                                    size={20}
                                    color="#F44336"
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </View>
            )}
        </View>
    );

    const renderLocationSection = () => (
        <View
            style={[styles.section, { backgroundColor: theme.CARD_BACKGROUND }]}
        >
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                Property Location *
            </Text>

            <TouchableOpacity
                style={[styles.addButton, { borderColor: theme.PRIMARY }]}
                onPress={handleLocationPicker}
            >
                <Ionicons name="location" size={24} color={theme.PRIMARY} />
                <Text style={[styles.addButtonText, { color: theme.PRIMARY }]}>
                    {selectedLocation ? 'Update Location' : 'Select Location'}
                </Text>
            </TouchableOpacity>

            {selectedLocation && (
                <View style={styles.mapContainer}>
                    <MapView
                        style={styles.map}
                        initialRegion={{
                            latitude: selectedLocation.latitude,
                            longitude: selectedLocation.longitude,
                            latitudeDelta: 0.01,
                            longitudeDelta: 0.01,
                        }}
                        onPress={(event) => {
                            setSelectedLocation(event.nativeEvent.coordinate);
                        }}
                    >
                        <Marker coordinate={selectedLocation} />
                    </MapView>
                    <Text
                        style={[
                            styles.mapHint,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Tap on map to adjust location
                    </Text>
                </View>
            )}
        </View>
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <BackButton color="#fff" />
                <Text style={styles.headerTitle}>Sell Your Land</Text>
                <View style={{ width: 24 }} />
            </LinearGradient>

            <Formik
                initialValues={{
                    title: '',
                    description: '',
                    price: '',
                    area: '',
                    areaUnit: 'sqft',
                    landType: '',
                    ownershipType: '',
                    location: '',
                }}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
            >
                {({
                    values,
                    errors,
                    touched,
                    handleChange,
                    handleBlur,
                    handleSubmit,
                    setFieldValue,
                }) => (
                    <ScrollView
                        style={styles.form}
                        showsVerticalScrollIndicator={false}
                    >
                        {/* Basic Information */}
                        <View
                            style={[
                                styles.section,
                                { backgroundColor: theme.CARD_BACKGROUND },
                            ]}
                        >
                            <Text
                                style={[
                                    styles.sectionTitle,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Basic Information
                            </Text>

                            <View style={styles.inputGroup}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Title *
                                </Text>
                                <TextInput
                                    style={[
                                        styles.input,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            color: theme.TEXT_PRIMARY,
                                        },
                                        touched.title &&
                                            errors.title &&
                                            styles.inputError,
                                    ]}
                                    placeholder="Enter property title"
                                    placeholderTextColor={
                                        theme.TEXT_PLACEHOLDER
                                    }
                                    value={values.title}
                                    onChangeText={handleChange('title')}
                                    onBlur={handleBlur('title')}
                                />
                                {touched.title && errors.title && (
                                    <Text style={styles.errorText}>
                                        {errors.title}
                                    </Text>
                                )}
                            </View>

                            <View style={styles.inputGroup}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Description *
                                </Text>
                                <TextInput
                                    style={[
                                        styles.textArea,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            color: theme.TEXT_PRIMARY,
                                        },
                                        touched.description &&
                                            errors.description &&
                                            styles.inputError,
                                    ]}
                                    placeholder="Describe your property in detail"
                                    placeholderTextColor={
                                        theme.TEXT_PLACEHOLDER
                                    }
                                    value={values.description}
                                    onChangeText={handleChange('description')}
                                    onBlur={handleBlur('description')}
                                    multiline
                                    numberOfLines={4}
                                />
                                {touched.description && errors.description && (
                                    <Text style={styles.errorText}>
                                        {errors.description}
                                    </Text>
                                )}
                            </View>

                            <View style={styles.inputGroup}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Location *
                                </Text>
                                <TextInput
                                    style={[
                                        styles.input,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            color: theme.TEXT_PRIMARY,
                                        },
                                        touched.location &&
                                            errors.location &&
                                            styles.inputError,
                                    ]}
                                    placeholder="Enter property address"
                                    placeholderTextColor={
                                        theme.TEXT_PLACEHOLDER
                                    }
                                    value={values.location}
                                    onChangeText={handleChange('location')}
                                    onBlur={handleBlur('location')}
                                />
                                {touched.location && errors.location && (
                                    <Text style={styles.errorText}>
                                        {errors.location}
                                    </Text>
                                )}
                            </View>
                        </View>

                        {/* Property Details */}
                        <View
                            style={[
                                styles.section,
                                { backgroundColor: theme.CARD_BACKGROUND },
                            ]}
                        >
                            <Text
                                style={[
                                    styles.sectionTitle,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Property Details
                            </Text>

                            <View style={styles.row}>
                                <View style={[styles.inputGroup, { flex: 2 }]}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Area *
                                    </Text>
                                    <TextInput
                                        style={[
                                            styles.input,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                color: theme.TEXT_PRIMARY,
                                            },
                                            touched.area &&
                                                errors.area &&
                                                styles.inputError,
                                        ]}
                                        placeholder="0"
                                        placeholderTextColor={
                                            theme.TEXT_PLACEHOLDER
                                        }
                                        value={values.area}
                                        onChangeText={handleChange('area')}
                                        onBlur={handleBlur('area')}
                                        keyboardType="numeric"
                                    />
                                    {touched.area && errors.area && (
                                        <Text style={styles.errorText}>
                                            {errors.area}
                                        </Text>
                                    )}
                                </View>

                                <View
                                    style={[
                                        styles.inputGroup,
                                        { flex: 1, marginLeft: 12 },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Unit *
                                    </Text>
                                    <TouchableOpacity
                                        style={[
                                            styles.picker,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                            },
                                        ]}
                                        onPress={() => {
                                            Alert.alert('Select Unit', '', [
                                                {
                                                    text: 'Sq Ft',
                                                    onPress: () =>
                                                        setFieldValue(
                                                            'areaUnit',
                                                            'sqft'
                                                        ),
                                                },
                                                {
                                                    text: 'Acres',
                                                    onPress: () =>
                                                        setFieldValue(
                                                            'areaUnit',
                                                            'acres'
                                                        ),
                                                },
                                                {
                                                    text: 'Hectares',
                                                    onPress: () =>
                                                        setFieldValue(
                                                            'areaUnit',
                                                            'hectares'
                                                        ),
                                                },
                                            ]);
                                        }}
                                    >
                                        <Text
                                            style={[
                                                styles.pickerText,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            {values.areaUnit}
                                        </Text>
                                        <Ionicons
                                            name="chevron-down"
                                            size={20}
                                            color={theme.TEXT_SECONDARY}
                                        />
                                    </TouchableOpacity>
                                </View>
                            </View>

                            <View style={styles.inputGroup}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Price (₹) *
                                </Text>
                                <TextInput
                                    style={[
                                        styles.input,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            color: theme.TEXT_PRIMARY,
                                        },
                                        touched.price &&
                                            errors.price &&
                                            styles.inputError,
                                    ]}
                                    placeholder="0"
                                    placeholderTextColor={
                                        theme.TEXT_PLACEHOLDER
                                    }
                                    value={values.price}
                                    onChangeText={handleChange('price')}
                                    onBlur={handleBlur('price')}
                                    keyboardType="numeric"
                                />
                                {touched.price && errors.price && (
                                    <Text style={styles.errorText}>
                                        {errors.price}
                                    </Text>
                                )}
                            </View>

                            <View style={styles.inputGroup}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Land Type *
                                </Text>
                                <TouchableOpacity
                                    style={[
                                        styles.picker,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                        },
                                    ]}
                                    onPress={() => {
                                        const options = categories.map(
                                            (cat) => ({
                                                text: cat.name,
                                                onPress: () =>
                                                    setFieldValue(
                                                        'landType',
                                                        cat.name
                                                    ),
                                            })
                                        );
                                        Alert.alert(
                                            'Select Land Type',
                                            '',
                                            options
                                        );
                                    }}
                                >
                                    <Text
                                        style={[
                                            styles.pickerText,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        {values.landType || 'Select land type'}
                                    </Text>
                                    <Ionicons
                                        name="chevron-down"
                                        size={20}
                                        color={theme.TEXT_SECONDARY}
                                    />
                                </TouchableOpacity>
                                {touched.landType && errors.landType && (
                                    <Text style={styles.errorText}>
                                        {errors.landType}
                                    </Text>
                                )}
                            </View>

                            <View style={styles.inputGroup}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Ownership Type *
                                </Text>
                                <TouchableOpacity
                                    style={[
                                        styles.picker,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                        },
                                    ]}
                                    onPress={() => {
                                        Alert.alert(
                                            'Select Ownership Type',
                                            '',
                                            [
                                                {
                                                    text: 'Freehold',
                                                    onPress: () =>
                                                        setFieldValue(
                                                            'ownershipType',
                                                            'Freehold'
                                                        ),
                                                },
                                                {
                                                    text: 'Leasehold',
                                                    onPress: () =>
                                                        setFieldValue(
                                                            'ownershipType',
                                                            'Leasehold'
                                                        ),
                                                },
                                                {
                                                    text: 'Co-operative',
                                                    onPress: () =>
                                                        setFieldValue(
                                                            'ownershipType',
                                                            'Co-operative'
                                                        ),
                                                },
                                            ]
                                        );
                                    }}
                                >
                                    <Text
                                        style={[
                                            styles.pickerText,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        {values.ownershipType ||
                                            'Select ownership type'}
                                    </Text>
                                    <Ionicons
                                        name="chevron-down"
                                        size={20}
                                        color={theme.TEXT_SECONDARY}
                                    />
                                </TouchableOpacity>
                                {touched.ownershipType &&
                                    errors.ownershipType && (
                                        <Text style={styles.errorText}>
                                            {errors.ownershipType}
                                        </Text>
                                    )}
                            </View>
                        </View>

                        {/* Images */}
                        {renderImageSection()}

                        {/* Documents */}
                        {renderDocumentSection()}

                        {/* Location */}
                        {renderLocationSection()}

                        {/* Submit Button */}
                        <TouchableOpacity
                            style={[
                                styles.submitButton,
                                { backgroundColor: theme.PRIMARY },
                                createListingMutation.isPending &&
                                    styles.submitButtonDisabled,
                            ]}
                            onPress={handleSubmit}
                            disabled={createListingMutation.isPending}
                        >
                            <Text style={styles.submitButtonText}>
                                {createListingMutation.isPending
                                    ? 'Creating Listing...'
                                    : 'Create Listing'}
                            </Text>
                        </TouchableOpacity>

                        <View style={{ height: 40 }} />
                    </ScrollView>
                )}
            </Formik>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingTop: 50,
        paddingBottom: 20,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    form: {
        flex: 1,
    },
    section: {
        margin: 16,
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    inputGroup: {
        marginBottom: 16,
    },
    label: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
    },
    input: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
        fontSize: 16,
    },
    textArea: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
        fontSize: 16,
        textAlignVertical: 'top',
        minHeight: 100,
    },
    picker: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
    },
    pickerText: {
        fontSize: 16,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    inputError: {
        borderWidth: 1,
        borderColor: '#F44336',
    },
    errorText: {
        color: '#F44336',
        fontSize: 12,
        marginTop: 4,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderWidth: 2,
        borderStyle: 'dashed',
        borderRadius: 8,
        marginBottom: 16,
    },
    addButtonText: {
        marginLeft: 8,
        fontSize: 16,
        fontWeight: '600',
    },
    imageList: {
        marginTop: 8,
    },
    imageItem: {
        position: 'relative',
        marginRight: 12,
    },
    selectedImage: {
        width: 100,
        height: 100,
        borderRadius: 8,
    },
    removeButton: {
        position: 'absolute',
        top: -8,
        right: -8,
        backgroundColor: '#fff',
        borderRadius: 10,
    },
    documentList: {
        marginTop: 8,
    },
    documentItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
        paddingHorizontal: 12,
        backgroundColor: '#f5f5f5',
        borderRadius: 8,
        marginBottom: 8,
    },
    documentName: {
        flex: 1,
        marginLeft: 8,
        fontSize: 14,
    },
    mapContainer: {
        marginTop: 16,
    },
    map: {
        height: 200,
        borderRadius: 8,
    },
    mapHint: {
        textAlign: 'center',
        marginTop: 8,
        fontSize: 12,
    },
    submitButton: {
        margin: 16,
        paddingVertical: 16,
        borderRadius: 12,
        alignItems: 'center',
    },
    submitButtonDisabled: {
        opacity: 0.6,
    },
    submitButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default SellLand;
