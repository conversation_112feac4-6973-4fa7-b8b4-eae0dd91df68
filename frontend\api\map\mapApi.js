import axios from 'axios';
import * as Location from 'expo-location';
import { privateAPIClient } from '../index';

const mapApi = axios.create({
    baseURL: `${privateAPIClient.defaults.baseURL}/api/map`,
});

// Add auth token to requests
mapApi.interceptors.request.use((config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Location Services
export const getCurrentLocation = async () => {
    try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
            throw new Error('Location permission not granted');
        }

        const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.High,
        });

        return {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy,
        };
    } catch (error) {
        throw new Error('Failed to get current location');
    }
};

export const watchLocation = async (callback, options = {}) => {
    try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
            throw new Error('Location permission not granted');
        }

        return await Location.watchPositionAsync(
            {
                accuracy: Location.Accuracy.High,
                timeInterval: options.timeInterval || 5000,
                distanceInterval: options.distanceInterval || 10,
            },
            callback
        );
    } catch (error) {
        throw new Error('Failed to watch location');
    }
};

// Geocoding Services
export const geocodeAddress = async (address) => {
    try {
        const geocoded = await Location.geocodeAsync(address);
        return geocoded;
    } catch (error) {
        throw new Error('Failed to geocode address');
    }
};

export const reverseGeocode = async (latitude, longitude) => {
    try {
        const reversed = await Location.reverseGeocodeAsync({
            latitude,
            longitude,
        });
        return reversed;
    } catch (error) {
        throw new Error('Failed to reverse geocode');
    }
};

// Land Location Services
export const getNearbyLands = async (latitude, longitude, radius = 10) => {
    const { data } = await mapApi.get('/lands/nearby', {
        params: {
            latitude,
            longitude,
            radius, // in kilometers
        },
    });
    return data;
};

export const searchLandsByLocation = async (searchParams) => {
    const { data } = await mapApi.post(
        '/lands/search-by-location',
        searchParams
    );
    return data;
};

export const getLandLocationDetails = async (landId) => {
    const { data } = await mapApi.get(`/lands/${landId}/location`);
    return data;
};

export const updateLandLocation = async (landId, locationData) => {
    const { data } = await mapApi.put(
        `/lands/${landId}/location`,
        locationData
    );
    return data;
};

// Broker Location Services
export const getNearbyBrokers = async (latitude, longitude, radius = 20) => {
    const { data } = await mapApi.get('/brokers/nearby', {
        params: {
            latitude,
            longitude,
            radius,
        },
    });
    return data;
};

// Enhanced service area matching function
const matchServiceArea = (serviceArea, locationName) => {
    if (!serviceArea || !locationName) return false;

    const areaLower = serviceArea.toLowerCase().trim();
    const locationLower = locationName.toLowerCase().trim();

    // Direct matches
    if (areaLower === locationLower) return true;
    if (locationLower.includes(areaLower) || areaLower.includes(locationLower))
        return true;

    // Common city name variations
    const cityVariations = {
        bangalore: ['bengaluru', 'blr'],
        bengaluru: ['bangalore', 'blr'],
        mumbai: ['bombay'],
        bombay: ['mumbai'],
        delhi: ['new delhi', 'ncr'],
        'new delhi': ['delhi', 'ncr'],
        chennai: ['madras'],
        madras: ['chennai'],
        kolkata: ['calcutta'],
        calcutta: ['kolkata'],
        pune: ['poona'],
        poona: ['pune'],
    };

    // Check variations
    for (const [key, variations] of Object.entries(cityVariations)) {
        if (
            areaLower.includes(key) &&
            variations.some((v) => locationLower.includes(v))
        ) {
            return true;
        }
        if (
            locationLower.includes(key) &&
            variations.some((v) => areaLower.includes(v))
        ) {
            return true;
        }
    }

    // Check for common area patterns (like "North Bangalore", "South Mumbai")
    const areaWords = areaLower.split(/\s+/);
    const locationWords = locationLower.split(/\s+/);

    // If any significant word matches (excluding common words)
    const commonWords = [
        'north',
        'south',
        'east',
        'west',
        'central',
        'new',
        'old',
    ];
    const significantAreaWords = areaWords.filter(
        (word) => word.length > 3 && !commonWords.includes(word)
    );
    const significantLocationWords = locationWords.filter(
        (word) => word.length > 3 && !commonWords.includes(word)
    );

    return significantAreaWords.some((areaWord) =>
        significantLocationWords.some(
            (locWord) =>
                areaWord.includes(locWord) || locWord.includes(areaWord)
        )
    );
};

// Filter brokers by service areas and location
export const filterBrokersByServiceArea = (
    brokers,
    latitude,
    longitude,
    radius,
    locationName
) => {
    if (!brokers || !Array.isArray(brokers)) return [];

    return brokers.filter((broker) => {
        // Always show brokers if they have service areas (for now, since we don't have coordinates)
        if (broker.serviceAreas && broker.serviceAreas.length > 0) {
            // Check if any service area matches the location
            const hasMatchingServiceArea = broker.serviceAreas.some((area) =>
                matchServiceArea(area, locationName)
            );

            // If we have location name, use service area matching
            if (locationName) {
                return hasMatchingServiceArea;
            }

            // If no location name, show all brokers with service areas
            return true;
        }

        // If broker has coordinates, check distance
        if (broker.coordinates || broker.location) {
            const brokerLocation = broker.coordinates || broker.location;
            if (brokerLocation.latitude && brokerLocation.longitude) {
                const distance = calculateDistance(
                    latitude,
                    longitude,
                    brokerLocation.latitude,
                    brokerLocation.longitude
                );
                return distance <= radius;
            }
        }

        // Default: show broker if no filtering criteria
        return true;
    });
};

export const assignBrokerByLocation = async (landId, userLocation) => {
    const { data } = await mapApi.post('/brokers/assign-by-location', {
        landId,
        userLocation,
    });
    return data;
};

export const getBrokerServiceAreas = async (brokerId) => {
    const { data } = await mapApi.get(`/brokers/${brokerId}/service-areas`);
    return data;
};

export const updateBrokerServiceArea = async (brokerId, serviceArea) => {
    const { data } = await mapApi.put(
        `/brokers/${brokerId}/service-area`,
        serviceArea
    );
    return data;
};

// Contractor Location Services
export const getNearbyContractors = async (
    latitude,
    longitude,
    radius = 50,
    serviceType = null
) => {
    const { data } = await mapApi.get('/contractors/nearby', {
        params: {
            latitude,
            longitude,
            radius,
            serviceType,
        },
    });
    return data;
};

// Filter contractors by service areas and location
export const filterContractorsByServiceArea = (
    contractors,
    latitude,
    longitude,
    radius,
    locationName
) => {
    if (!contractors || !Array.isArray(contractors)) return [];

    return contractors.filter((contractor) => {
        // Always show contractors if they have service areas (for now, since we don't have coordinates)
        if (contractor.serviceAreas && contractor.serviceAreas.length > 0) {
            // Check if any service area matches the location
            const hasMatchingServiceArea = contractor.serviceAreas.some(
                (area) => matchServiceArea(area, locationName)
            );

            // If we have location name, use service area matching
            if (locationName) {
                return hasMatchingServiceArea;
            }

            // If no location name, show all contractors with service areas
            return true;
        }

        // If contractor has coordinates, check distance
        if (contractor.coordinates || contractor.location) {
            const contractorLocation =
                contractor.coordinates || contractor.location;
            if (contractorLocation.latitude && contractorLocation.longitude) {
                const distance = calculateDistance(
                    latitude,
                    longitude,
                    contractorLocation.latitude,
                    contractorLocation.longitude
                );
                return distance <= radius;
            }
        }

        // Default: show contractor if no filtering criteria
        return true;
    });
};

export const getContractorServiceAreas = async (contractorId) => {
    const { data } = await mapApi.get(
        `/contractors/${contractorId}/service-areas`
    );
    return data;
};

export const updateContractorServiceArea = async (
    contractorId,
    serviceArea
) => {
    const { data } = await mapApi.put(
        `/contractors/${contractorId}/service-area`,
        serviceArea
    );
    return data;
};

// Route and Distance Services
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
            Math.cos((lat2 * Math.PI) / 180) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    return distance;
};

export const getRouteDirections = async (
    origin,
    destination,
    mode = 'driving'
) => {
    const { data } = await mapApi.post('/directions', {
        origin,
        destination,
        mode, // 'driving', 'walking', 'transit'
    });
    return data;
};

export const getEstimatedTravelTime = async (
    origin,
    destination,
    mode = 'driving'
) => {
    const { data } = await mapApi.post('/travel-time', {
        origin,
        destination,
        mode,
    });
    return data;
};

// Area and Boundary Services
export const getAreaBoundaries = async (areaType, areaId) => {
    const { data } = await mapApi.get(`/boundaries/${areaType}/${areaId}`);
    return data;
};

export const getCityBoundaries = async (cityName) => {
    const { data } = await mapApi.get(
        `/boundaries/city/${encodeURIComponent(cityName)}`
    );
    return data;
};

export const getStateBoundaries = async (stateName) => {
    const { data } = await mapApi.get(
        `/boundaries/state/${encodeURIComponent(stateName)}`
    );
    return data;
};

// Points of Interest
export const getNearbyPOIs = async (
    latitude,
    longitude,
    category = 'all',
    radius = 5
) => {
    const { data } = await mapApi.get('/poi/nearby', {
        params: {
            latitude,
            longitude,
            category, // 'schools', 'hospitals', 'banks', 'transport', 'all'
            radius,
        },
    });
    return data;
};

export const getLandPOIAnalysis = async (landId) => {
    const { data } = await mapApi.get(`/poi/land-analysis/${landId}`);
    return data;
};

// Map Layers and Overlays
export const getMapLayers = async (layerType) => {
    const { data } = await mapApi.get(`/layers/${layerType}`);
    return data;
};

export const getTrafficData = async (bounds) => {
    const { data } = await mapApi.post('/traffic', { bounds });
    return data;
};

export const getWeatherOverlay = async (latitude, longitude) => {
    const { data } = await mapApi.get('/weather', {
        params: { latitude, longitude },
    });
    return data;
};

// Location Analytics
export const getLocationAnalytics = async (latitude, longitude, radius = 5) => {
    const { data } = await mapApi.get('/analytics/location', {
        params: {
            latitude,
            longitude,
            radius,
        },
    });
    return data;
};

export const getLandValueByLocation = async (latitude, longitude) => {
    const { data } = await mapApi.get('/analytics/land-value', {
        params: { latitude, longitude },
    });
    return data;
};

export const getAreaDevelopmentIndex = async (latitude, longitude) => {
    const { data } = await mapApi.get('/analytics/development-index', {
        params: { latitude, longitude },
    });
    return data;
};

// Saved Locations
export const saveUserLocation = async (userId, locationData) => {
    const { data } = await mapApi.post(
        `/users/${userId}/locations`,
        locationData
    );
    return data;
};

export const getUserSavedLocations = async (userId) => {
    const { data } = await mapApi.get(`/users/${userId}/locations`);
    return data;
};

export const deleteUserLocation = async (userId, locationId) => {
    const { data } = await mapApi.delete(
        `/users/${userId}/locations/${locationId}`
    );
    return data;
};

// Location Sharing
export const shareLocation = async (
    userId,
    locationData,
    shareWith,
    duration = 3600
) => {
    const { data } = await mapApi.post('/location/share', {
        userId,
        locationData,
        shareWith, // array of user IDs
        duration, // in seconds
    });
    return data;
};

export const getSharedLocations = async (userId) => {
    const { data } = await mapApi.get(`/location/shared/${userId}`);
    return data;
};

export const stopLocationSharing = async (shareId) => {
    const { data } = await mapApi.delete(`/location/share/${shareId}`);
    return data;
};

// Geofencing
export const createGeofence = async (geofenceData) => {
    const { data } = await mapApi.post('/geofence', geofenceData);
    return data;
};

export const getUserGeofences = async (userId) => {
    const { data } = await mapApi.get(`/geofence/user/${userId}`);
    return data;
};

export const checkGeofenceEntry = async (userId, latitude, longitude) => {
    const { data } = await mapApi.post('/geofence/check', {
        userId,
        latitude,
        longitude,
    });
    return data;
};

export const deleteGeofence = async (geofenceId) => {
    const { data } = await mapApi.delete(`/geofence/${geofenceId}`);
    return data;
};

// Map Utilities
export const formatCoordinates = (latitude, longitude, format = 'decimal') => {
    if (format === 'dms') {
        // Convert to degrees, minutes, seconds
        const latDMS = convertToDMS(latitude, 'lat');
        const lonDMS = convertToDMS(longitude, 'lon');
        return `${latDMS}, ${lonDMS}`;
    }
    return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
};

const convertToDMS = (coordinate, type) => {
    const absolute = Math.abs(coordinate);
    const degrees = Math.floor(absolute);
    const minutesNotTruncated = (absolute - degrees) * 60;
    const minutes = Math.floor(minutesNotTruncated);
    const seconds = Math.floor((minutesNotTruncated - minutes) * 60);

    const direction =
        type === 'lat'
            ? coordinate >= 0
                ? 'N'
                : 'S'
            : coordinate >= 0
              ? 'E'
              : 'W';

    return `${degrees}°${minutes}'${seconds}"${direction}`;
};

export const isLocationWithinBounds = (location, bounds) => {
    return (
        location.latitude >= bounds.southwest.latitude &&
        location.latitude <= bounds.northeast.latitude &&
        location.longitude >= bounds.southwest.longitude &&
        location.longitude <= bounds.northeast.longitude
    );
};

export const getBoundsFromLocations = (locations) => {
    if (!locations || locations.length === 0) return null;

    let minLat = locations[0].latitude;
    let maxLat = locations[0].latitude;
    let minLon = locations[0].longitude;
    let maxLon = locations[0].longitude;

    locations.forEach((location) => {
        minLat = Math.min(minLat, location.latitude);
        maxLat = Math.max(maxLat, location.latitude);
        minLon = Math.min(minLon, location.longitude);
        maxLon = Math.max(maxLon, location.longitude);
    });

    return {
        southwest: { latitude: minLat, longitude: minLon },
        northeast: { latitude: maxLat, longitude: maxLon },
    };
};

export default mapApi;
