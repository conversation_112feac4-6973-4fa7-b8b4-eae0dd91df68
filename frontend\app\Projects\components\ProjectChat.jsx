import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Alert,
    Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';
import { AuthContext } from '../../../context/AuthContext';

const ProjectChat = ({ projectId, project }) => {
    const { theme } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();

    const handleOpenChat = () => {
        if (!project.contractorId && !project.brokerId) {
            Alert.alert(
                'Info',
                'Chat will be available once contractor and broker are assigned'
            );
            return;
        }

        // Navigate to existing chat screen (assuming you have a chat implementation)
        // This would use your existing frontend chat system
        router.push(`/Chat?projectId=${projectId}`);
    };

    const handleMakeCall = () => {
        // Get phone numbers of participants
        const participants = getParticipantsForCall();

        if (participants.length === 0) {
            Alert.alert('Info', 'No participants available for call');
            return;
        }

        if (participants.length === 1) {
            // Direct call to single participant
            const phoneNumber = participants[0].phone;
            if (phoneNumber) {
                Linking.openURL(`tel:${phoneNumber}`);
            } else {
                Alert.alert('Error', 'Phone number not available');
            }
        } else {
            // Show options for multiple participants
            Alert.alert(
                'Select Contact',
                'Choose who to call:',
                participants
                    .map((participant) => ({
                        text: `${participant.name} (${participant.role})`,
                        onPress: () => {
                            if (participant.phone) {
                                Linking.openURL(`tel:${participant.phone}`);
                            } else {
                                Alert.alert(
                                    'Error',
                                    'Phone number not available'
                                );
                            }
                        },
                    }))
                    .concat([{ text: 'Cancel', style: 'cancel' }])
            );
        }
    };

    const getParticipantsForCall = () => {
        const participants = [];

        // Add contractor if assigned and not current user
        if (project.contractorId && project.contractorId !== user?.id) {
            participants.push({
                id: project.contractorId,
                name: 'Contractor', // You might want to fetch actual name
                role: 'contractor',
                phone: null, // You'll need to fetch this from user data
            });
        }

        // Add broker if assigned and not current user
        if (project.brokerId && project.brokerId !== user?.id) {
            participants.push({
                id: project.brokerId,
                name: 'Broker', // You might want to fetch actual name
                role: 'broker',
                phone: null, // You'll need to fetch this from user data
            });
        }

        // Add project owner if current user is not the owner
        if (project.userId !== user?.id) {
            participants.push({
                id: project.userId,
                name: 'Project Owner',
                role: 'user',
                phone: null, // You'll need to fetch this from user data
            });
        }

        return participants;
    };

    const getChatStatus = () => {
        if (!project.contractorId && !project.brokerId) return 'Chat pending';
        return 'Chat available';
    };

    const getParticipantCount = () => {
        let count = 1; // Project owner
        if (project.contractorId) count++;
        if (project.brokerId) count++;
        return count;
    };

    const canChat = project.contractorId || project.brokerId;
    const canCall = project.contractorId || project.brokerId;

    return (
        <View style={[styles.container, { backgroundColor: theme.CARD }]}>
            <View style={styles.header}>
                <View style={styles.titleContainer}>
                    <Ionicons
                        name="chatbubbles-outline"
                        size={24}
                        color={theme.PRIMARY}
                    />
                    <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                        Project Communication
                    </Text>
                </View>
                <Text style={[styles.status, { color: theme.TEXT_SECONDARY }]}>
                    {getChatStatus()}
                </Text>
            </View>

            <View style={styles.participantsInfo}>
                <Text
                    style={[
                        styles.participantsText,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {getParticipantCount()} participant
                    {getParticipantCount() > 1 ? 's' : ''}
                </Text>
                <View style={styles.participantsList}>
                    <View style={styles.participant}>
                        <Ionicons
                            name="person"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.participantRole,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Project Owner
                        </Text>
                    </View>
                    {project.contractorId && (
                        <View style={styles.participant}>
                            <Ionicons
                                name="hammer"
                                size={16}
                                color={theme.TEXT_SECONDARY}
                            />
                            <Text
                                style={[
                                    styles.participantRole,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Contractor
                            </Text>
                        </View>
                    )}
                    {project.brokerId && (
                        <View style={styles.participant}>
                            <Ionicons
                                name="business"
                                size={16}
                                color={theme.TEXT_SECONDARY}
                            />
                            <Text
                                style={[
                                    styles.participantRole,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Broker
                            </Text>
                        </View>
                    )}
                </View>
            </View>

            <View style={styles.actions}>
                <TouchableOpacity
                    style={[
                        styles.actionButton,
                        styles.chatButton,
                        {
                            backgroundColor: canChat
                                ? theme.PRIMARY
                                : theme.BACKGROUND,
                            borderColor: theme.BORDER,
                        },
                    ]}
                    onPress={handleOpenChat}
                    disabled={!canChat}
                >
                    <Ionicons
                        name="chatbubble"
                        size={20}
                        color={canChat ? theme.WHITE : theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.actionButtonText,
                            {
                                color: canChat
                                    ? theme.WHITE
                                    : theme.TEXT_SECONDARY,
                            },
                        ]}
                    >
                        Open Chat
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[
                        styles.actionButton,
                        styles.callButton,
                        {
                            backgroundColor: canCall
                                ? theme.SUCCESS
                                : theme.BACKGROUND,
                            borderColor: theme.BORDER,
                        },
                    ]}
                    onPress={handleMakeCall}
                    disabled={!canCall}
                >
                    <Ionicons
                        name="call"
                        size={20}
                        color={canCall ? theme.WHITE : theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.actionButtonText,
                            {
                                color: canCall
                                    ? theme.WHITE
                                    : theme.TEXT_SECONDARY,
                            },
                        ]}
                    >
                        Call
                    </Text>
                </TouchableOpacity>
            </View>

            {!canChat && !canCall && (
                <View style={styles.infoContainer}>
                    <Ionicons
                        name="information-circle-outline"
                        size={20}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.infoText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Communication will be available once contractor and
                        broker are assigned to the project
                    </Text>
                </View>
            )}

            {canChat && (
                <View style={styles.chatInfo}>
                    <Text
                        style={[
                            styles.chatInfoText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Use your existing chat system to communicate with
                        project participants
                    </Text>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
        borderRadius: 12,
        margin: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    titleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
    },
    status: {
        fontSize: 12,
        fontStyle: 'italic',
    },
    participantsInfo: {
        marginBottom: 16,
    },
    participantsText: {
        fontSize: 14,
        marginBottom: 8,
    },
    participantsList: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
    },
    participant: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    participantRole: {
        fontSize: 12,
    },
    actions: {
        flexDirection: 'row',
        gap: 12,
        marginBottom: 16,
    },
    actionButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
        borderWidth: 1,
        gap: 8,
    },
    chatButton: {
        // Specific styles for chat button if needed
    },
    callButton: {
        // Specific styles for call button if needed
    },
    actionButtonText: {
        fontSize: 14,
        fontWeight: '500',
    },
    infoContainer: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 8,
        padding: 12,
        borderRadius: 8,
        backgroundColor: 'rgba(0,0,0,0.05)',
    },
    infoText: {
        fontSize: 12,
        lineHeight: 16,
        flex: 1,
    },
    chatInfo: {
        marginTop: 8,
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    chatInfoText: {
        fontSize: 12,
        textAlign: 'center',
    },
});

export default ProjectChat;
