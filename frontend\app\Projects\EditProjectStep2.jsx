import React from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    ScrollView,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';

const EditProjectStep2 = ({ formik, theme, setStep, isSubmitting, styles }) => {
    const {
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        setFieldValue,
        handleSubmit,
    } = formik;

    return (
        <>
            {/* Design Preferences Section */}
            <View style={styles.sectionHeader}>
                <MaterialIcons name="home" size={20} color={theme.PRIMARY} />
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    Design Preferences
                </Text>
            </View>

            <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1, marginRight: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Floors
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.floors && touched.floors
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.floors &&
                                touched.floors &&
                                styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="layers-outline"
                            size={20}
                            color={
                                errors.floors && touched.floors
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                {
                                    color: theme.TEXT_PRIMARY,
                                },
                            ]}
                            value={values.floors}
                            onChangeText={handleChange('floors')}
                            onBlur={handleBlur('floors')}
                            keyboardType="numeric"
                        />
                    </View>
                    {errors.floors && touched.floors && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.floors}
                        </Text>
                    )}
                </View>

                <View
                    style={[
                        styles.inputGroup,
                        { flex: 1, marginHorizontal: 4 },
                    ]}
                >
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Bedrooms
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.bedrooms && touched.bedrooms
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.bedrooms &&
                                touched.bedrooms &&
                                styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="bed-outline"
                            size={20}
                            color={
                                errors.bedrooms && touched.bedrooms
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                {
                                    color: theme.TEXT_PRIMARY,
                                },
                            ]}
                            value={values.bedrooms}
                            onChangeText={handleChange('bedrooms')}
                            onBlur={handleBlur('bedrooms')}
                            keyboardType="numeric"
                        />
                    </View>
                    {errors.bedrooms && touched.bedrooms && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.bedrooms}
                        </Text>
                    )}
                </View>

                <View style={[styles.inputGroup, { flex: 1, marginLeft: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Bathrooms
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.bathrooms && touched.bathrooms
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.bathrooms &&
                                touched.bathrooms &&
                                styles.inputError,
                        ]}
                    >
                        <MaterialIcons
                            name="bathroom"
                            size={20}
                            color={
                                errors.bathrooms && touched.bathrooms
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                {
                                    color: theme.TEXT_PRIMARY,
                                },
                            ]}
                            value={values.bathrooms}
                            onChangeText={handleChange('bathrooms')}
                            onBlur={handleBlur('bathrooms')}
                            keyboardType="numeric"
                        />
                    </View>
                    {errors.bathrooms && touched.bathrooms && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.bathrooms}
                        </Text>
                    )}
                </View>
            </View>

            {/* Design Preferences */}
            <View style={styles.preferencesContainer}>
                {/* Parking Required */}
                <View
                    style={[
                        styles.preferenceItem,
                        { borderColor: theme.INPUT_BORDER },
                    ]}
                >
                    <View style={styles.preferenceHeader}>
                        <Ionicons
                            name="car-outline"
                            size={20}
                            color={theme.PRIMARY}
                            style={styles.preferenceIcon}
                        />
                        <View style={styles.preferenceLabelContainer}>
                            <Text
                                style={[
                                    styles.preferenceLabel,
                                    {
                                        color: theme.TEXT_PRIMARY,
                                    },
                                ]}
                            >
                                Parking Required
                            </Text>
                            <Text
                                style={[
                                    styles.preferenceDescription,
                                    {
                                        color: theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Dedicated parking space needed
                            </Text>
                        </View>
                    </View>
                    <View
                        style={[
                            styles.toggleButtonContainer,
                            {
                                borderColor: theme.INPUT_BORDER,
                            },
                        ]}
                    >
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonLeft,
                                {
                                    backgroundColor: !values.parkingRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BACKGROUND,
                                    borderColor: !values.parkingRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('parkingRequired', false)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: !values.parkingRequired
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                No
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonRight,
                                {
                                    backgroundColor: values.parkingRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BACKGROUND,
                                    borderColor: values.parkingRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('parkingRequired', true)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: values.parkingRequired
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Yes
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Garden Required */}
                <View
                    style={[
                        styles.preferenceItem,
                        { borderColor: theme.INPUT_BORDER },
                    ]}
                >
                    <View style={styles.preferenceHeader}>
                        <Ionicons
                            name="leaf-outline"
                            size={20}
                            color={theme.PRIMARY}
                            style={styles.preferenceIcon}
                        />
                        <View style={styles.preferenceLabelContainer}>
                            <Text
                                style={[
                                    styles.preferenceLabel,
                                    {
                                        color: theme.TEXT_PRIMARY,
                                    },
                                ]}
                            >
                                Garden Required
                            </Text>
                            <Text
                                style={[
                                    styles.preferenceDescription,
                                    {
                                        color: theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Green space or garden area
                            </Text>
                        </View>
                    </View>
                    <View
                        style={[
                            styles.toggleButtonContainer,
                            {
                                borderColor: theme.INPUT_BORDER,
                            },
                        ]}
                    >
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonLeft,
                                {
                                    backgroundColor: !values.gardenRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BACKGROUND,
                                    borderColor: !values.gardenRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('gardenRequired', false)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: !values.gardenRequired
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                No
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonRight,
                                {
                                    backgroundColor: values.gardenRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BACKGROUND,
                                    borderColor: values.gardenRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('gardenRequired', true)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: values.gardenRequired
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Yes
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Vastu Compliance */}
                <View
                    style={[
                        styles.preferenceItem,
                        { borderColor: theme.INPUT_BORDER },
                    ]}
                >
                    <View style={styles.preferenceHeader}>
                        <Ionicons
                            name="compass-outline"
                            size={20}
                            color={theme.PRIMARY}
                            style={styles.preferenceIcon}
                        />
                        <View style={styles.preferenceLabelContainer}>
                            <Text
                                style={[
                                    styles.preferenceLabel,
                                    {
                                        color: theme.TEXT_PRIMARY,
                                    },
                                ]}
                            >
                                Vastu Compliance
                            </Text>
                            <Text
                                style={[
                                    styles.preferenceDescription,
                                    {
                                        color: theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Follow traditional Vastu principles
                            </Text>
                        </View>
                    </View>
                    <View
                        style={[
                            styles.toggleButtonContainer,
                            {
                                borderColor: theme.INPUT_BORDER,
                            },
                        ]}
                    >
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonLeft,
                                {
                                    backgroundColor: !values.vastuCompliance
                                        ? theme.PRIMARY
                                        : theme.INPUT_BACKGROUND,
                                    borderColor: !values.vastuCompliance
                                        ? theme.PRIMARY
                                        : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('vastuCompliance', false)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: !values.vastuCompliance
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                No
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonRight,
                                {
                                    backgroundColor: values.vastuCompliance
                                        ? theme.PRIMARY
                                        : theme.INPUT_BACKGROUND,
                                    borderColor: values.vastuCompliance
                                        ? theme.PRIMARY
                                        : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('vastuCompliance', true)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: values.vastuCompliance
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Yes
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Site Scout Assistance Required */}
                <View
                    style={[
                        styles.preferenceItem,
                        { borderColor: theme.INPUT_BORDER },
                    ]}
                >
                    <View style={styles.preferenceHeader}>
                        <Ionicons
                            name="people-outline"
                            size={20}
                            color={theme.PRIMARY}
                            style={styles.preferenceIcon}
                        />
                        <View style={styles.preferenceLabelContainer}>
                            <Text
                                style={[
                                    styles.preferenceLabel,
                                    {
                                        color: theme.TEXT_PRIMARY,
                                    },
                                ]}
                            >
                                Site Scout Assistance Required
                            </Text>
                            <Text
                                style={[
                                    styles.preferenceDescription,
                                    {
                                        color: theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Get help from assured site scouts
                            </Text>
                        </View>
                    </View>
                    <View
                        style={[
                            styles.toggleButtonContainer,
                            {
                                borderColor: theme.INPUT_BORDER,
                            },
                        ]}
                    >
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonLeft,
                                {
                                    backgroundColor:
                                        !values.brokerAssistanceRequired
                                            ? theme.PRIMARY
                                            : theme.INPUT_BACKGROUND,
                                    borderColor:
                                        !values.brokerAssistanceRequired
                                            ? theme.PRIMARY
                                            : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('brokerAssistanceRequired', false)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: !values.brokerAssistanceRequired
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                No
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.toggleButton,
                                styles.toggleButtonRight,
                                {
                                    backgroundColor:
                                        values.brokerAssistanceRequired
                                            ? theme.PRIMARY
                                            : theme.INPUT_BACKGROUND,
                                    borderColor: values.brokerAssistanceRequired
                                        ? theme.PRIMARY
                                        : theme.INPUT_BORDER,
                                },
                            ]}
                            onPress={() =>
                                setFieldValue('brokerAssistanceRequired', true)
                            }
                            activeOpacity={0.7}
                        >
                            <Text
                                style={[
                                    styles.toggleButtonText,
                                    {
                                        color: values.brokerAssistanceRequired
                                            ? theme.WHITE
                                            : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                Yes
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>

            {/* Additional Details Container */}
            <View style={styles.additionalDetailsContainer}>
                {/* Additional Facilities */}
                <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Additional Facilities
                    </Text>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        style={styles.capsuleScrollView}
                        contentContainerStyle={styles.capsuleContainer}
                    >
                        {[
                            'Swimming Pool',
                            'Gym',
                            'Garden',
                            'Parking',
                            'Security',
                            'Playground',
                            'Club House',
                            'Elevator',
                            'Power Backup',
                            'Water Supply',
                            'CCTV',
                            'Intercom',
                        ].map((facility) => {
                            const isSelected = values.additionalFacilities
                                .split(',')
                                .map((f) => f.trim())
                                .filter((f) => f)
                                .includes(facility);

                            return (
                                <TouchableOpacity
                                    key={facility}
                                    style={[
                                        styles.capsule,
                                        {
                                            backgroundColor: isSelected
                                                ? theme.PRIMARY
                                                : theme.INPUT_BACKGROUND,
                                            borderColor: isSelected
                                                ? theme.PRIMARY
                                                : theme.INPUT_BORDER,
                                        },
                                    ]}
                                    onPress={() => {
                                        const currentFacilities =
                                            values.additionalFacilities
                                                .split(',')
                                                .map((f) => f.trim())
                                                .filter((f) => f);

                                        let updatedFacilities;
                                        if (isSelected) {
                                            updatedFacilities =
                                                currentFacilities.filter(
                                                    (f) => f !== facility
                                                );
                                        } else {
                                            updatedFacilities = [
                                                ...currentFacilities,
                                                facility,
                                            ];
                                        }

                                        setFieldValue(
                                            'additionalFacilities',
                                            updatedFacilities.join(', ')
                                        );
                                    }}
                                    activeOpacity={0.7}
                                >
                                    <Text
                                        style={[
                                            styles.capsuleText,
                                            {
                                                color: isSelected
                                                    ? theme.WHITE
                                                    : theme.TEXT_PRIMARY,
                                            },
                                        ]}
                                    >
                                        {facility}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                    </ScrollView>
                </View>

                {/* Special Instructions */}
                <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Special Instructions
                    </Text>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        style={styles.capsuleScrollView}
                        contentContainerStyle={styles.capsuleContainer}
                    >
                        {[
                            'Eco-friendly materials',
                            'Quick completion',
                            'Budget-friendly',
                            'Premium finishes',
                            'Energy efficient',
                            'Modern design',
                            'Traditional style',
                            'Minimal maintenance',
                            'Smart home features',
                            'Natural lighting',
                            'Open floor plan',
                            'Privacy focused',
                        ].map((instruction) => {
                            const isSelected = values.specialInstructions
                                .split(',')
                                .map((i) => i.trim())
                                .filter((i) => i)
                                .includes(instruction);

                            return (
                                <TouchableOpacity
                                    key={instruction}
                                    style={[
                                        styles.capsule,
                                        {
                                            backgroundColor: isSelected
                                                ? theme.PRIMARY
                                                : theme.INPUT_BACKGROUND,
                                            borderColor: isSelected
                                                ? theme.PRIMARY
                                                : theme.INPUT_BORDER,
                                        },
                                    ]}
                                    onPress={() => {
                                        const currentInstructions =
                                            values.specialInstructions
                                                .split(',')
                                                .map((i) => i.trim())
                                                .filter((i) => i);

                                        let updatedInstructions;
                                        if (isSelected) {
                                            updatedInstructions =
                                                currentInstructions.filter(
                                                    (i) => i !== instruction
                                                );
                                        } else {
                                            updatedInstructions = [
                                                ...currentInstructions,
                                                instruction,
                                            ];
                                        }

                                        setFieldValue(
                                            'specialInstructions',
                                            updatedInstructions.join(', ')
                                        );
                                    }}
                                    activeOpacity={0.7}
                                >
                                    <Text
                                        style={[
                                            styles.capsuleText,
                                            {
                                                color: isSelected
                                                    ? theme.WHITE
                                                    : theme.TEXT_PRIMARY,
                                            },
                                        ]}
                                    >
                                        {instruction}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                    </ScrollView>
                </View>
            </View>

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        {
                            flex: 1,
                            marginRight: 8,
                            borderColor: theme.PRIMARY,
                        },
                    ]}
                    onPress={() => setStep(1)}
                    accessibilityLabel="Go back to step 1"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.WHITE, theme.GRAY_LIGHT]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Back
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        {
                            flex: 1,
                            marginLeft: 8,
                            borderColor: theme.ACCENT,
                            opacity: isSubmitting ? 0.7 : 1,
                        },
                    ]}
                    onPress={handleSubmit}
                    disabled={isSubmitting}
                    accessibilityLabel="Update project"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        {isSubmitting ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator
                                    color={theme.WHITE}
                                    size="small"
                                    style={{ marginRight: 8 }}
                                />
                                <Text
                                    style={[
                                        styles.submitButtonText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Updating...
                                </Text>
                            </View>
                        ) : (
                            <Text
                                style={[
                                    styles.submitButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Update Project
                            </Text>
                        )}
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default EditProjectStep2;
