import React, { useContext, useState, useRef, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    Alert,
    Dimensions,
    Animated,
    Easing,
    Image,
    ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import BackButton from '../Components/Shared/BackButton';
import { useQuery } from '@tanstack/react-query';
import { fetchProjectDetails } from '../../api/projects/projectApi';
import Toast from 'react-native-toast-message';
import ProgressTracker from './components/ProgressTracker';
import ProjectChat from './components/ProjectChat';

const { width, height } = Dimensions.get('window');

export default function ProjectDetails() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();
    const { id } = useLocalSearchParams();

    // Animation refs
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    const {
        data: projectData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['projectDetails', id],
        queryFn: () => fetchProjectDetails(id),
        enabled: !!id,
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to fetch project details',
            });
        },
    });

    // Initialize animations
    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const project = projectData?.project;
    const requirement = projectData?.requirement;

    // Merge project and requirement data for easier access
    const mergedProject =
        project && requirement
            ? {
                  ...project,
                  projectType: requirement.projectType,
                  constructionType: requirement.constructionType,
                  location: requirement.location,
                  budget: requirement.budget,
                  designPreferences: requirement.designPreferences,
                  additionalFacilities: requirement.additionalFacilities,
                  brokerAssistanceRequired:
                      requirement.brokerAssistanceRequired,
                  specialInstructions: requirement.specialInstructions,
                  expectedStartDate: requirement.expectedStartDate,
                  expectedCompletionDate: requirement.expectedCompletionDate,
              }
            : project;

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget))
            return 'Not specified';

        const formatAmount = (amount) => {
            if (amount >= 10000000)
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
            if (amount >= 1000) return `₹${(amount / 1000).toFixed(1)}K`;
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        }
        if (budget.minBudget) return `From ${formatAmount(budget.minBudget)}`;
        if (budget.maxBudget) return `Up to ${formatAmount(budget.maxBudget)}`;
        return 'Not specified';
    };

    const handleHireBroker = () => {
        if (!mergedProject?.brokerAssistanceRequired) {
            Alert.alert(
                'Broker Not Required',
                'This project does not require broker assistance.',
                [{ text: 'OK' }]
            );
            return;
        }
        router.push(`/Projects/HireProfessionals?projectId=${id}&type=broker`);
    };

    const handleHireContractor = () => {
        if (
            mergedProject?.brokerAssistanceRequired &&
            !mergedProject?.brokerId
        ) {
            Alert.alert(
                'Broker Required First',
                'You need to hire a broker before hiring a contractor for this project.',
                [{ text: 'OK' }]
            );
            return;
        }
        router.push(
            `/Projects/HireProfessionals?projectId=${id}&type=contractor`
        );
    };

    const InfoSection = ({ title, icon, children }) => (
        <View
            style={[
                styles.section,
                {
                    backgroundColor: theme.CARD,
                    shadowColor: theme.PRIMARY,
                },
            ]}
        >
            <View style={styles.sectionHeader}>
                <View
                    style={[
                        styles.sectionIcon,
                        { backgroundColor: theme.PRIMARY + '20' },
                    ]}
                >
                    <MaterialIcons
                        name={icon}
                        size={20}
                        color={theme.PRIMARY}
                    />
                </View>
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    {title}
                </Text>
            </View>
            <View style={styles.sectionContent}>{children}</View>
        </View>
    );

    const InfoRow = ({ label, value, icon }) => (
        <View style={styles.infoRow}>
            {icon && (
                <MaterialIcons name={icon} size={16} color={theme.PRIMARY} />
            )}
            <View style={styles.infoContent}>
                <Text
                    style={[styles.infoLabel, { color: theme.TEXT_SECONDARY }]}
                >
                    {label}
                </Text>
                <Text style={[styles.infoValue, { color: theme.TEXT_PRIMARY }]}>
                    {value || 'Not specified'}
                </Text>
            </View>
        </View>
    );

    if (isLoading) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                <View style={[styles.header, { backgroundColor: theme.CARD }]}>
                    <BackButton
                        color={theme.TEXT_PRIMARY}
                        onPress={() => router.back()}
                    />
                    <Text
                        style={[
                            styles.headerTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Project Details
                    </Text>
                    <View style={{ width: 24 }} />
                </View>
                <View style={styles.loadingContainer}>
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Loading project details...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (!mergedProject) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                <View style={[styles.header, { backgroundColor: theme.CARD }]}>
                    <BackButton
                        color={theme.TEXT_PRIMARY}
                        onPress={() => router.back()}
                    />
                    <Text
                        style={[
                            styles.headerTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Project Details
                    </Text>
                    <View style={{ width: 24 }} />
                </View>
                <View style={styles.errorContainer}>
                    <MaterialIcons
                        name="error-outline"
                        size={60}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.errorText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Project not found
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Background Pattern */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            <SafeAreaView style={styles.safeArea}>
                {/* Header with Gradient */}
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    style={styles.header}
                >
                    <View style={styles.headerContent}>
                        <TouchableOpacity onPress={() => router.back()}>
                            <Ionicons
                                name="arrow-back"
                                size={24}
                                color="#fff"
                            />
                        </TouchableOpacity>
                        <Text style={styles.headerTitle} numberOfLines={1}>
                            {mergedProject.projectName}
                        </Text>
                        {/* Only show edit button for project owners */}
                        {mergedProject.userRoleInProject === 'owner' ||
                        mergedProject.userId === user?.id ? (
                            <TouchableOpacity
                                onPress={() =>
                                    router.push(
                                        `/Projects/EditProject?id=${id}`
                                    )
                                }
                            >
                                <Ionicons
                                    name="create-outline"
                                    size={24}
                                    color="#fff"
                                />
                            </TouchableOpacity>
                        ) : (
                            <View style={{ width: 24 }} />
                        )}
                    </View>
                </LinearGradient>

                <ScrollView
                    style={styles.scrollView}
                    showsVerticalScrollIndicator={false}
                >
                    <Animated.View
                        style={[
                            styles.content,
                            {
                                transform: [{ scale: scaleAnim }],
                                opacity: fadeAnim,
                            },
                        ]}
                    >
                        {/* Project Overview */}
                        <InfoSection title="Project Overview" icon="assignment">
                            <InfoRow
                                label="Project Type"
                                value={
                                    mergedProject.projectType || 'Not specified'
                                }
                            />
                            <InfoRow
                                label="Construction Type"
                                value={
                                    mergedProject.constructionType ||
                                    'Not specified'
                                }
                            />
                            <InfoRow label="Status" value="Active" />
                            <InfoRow
                                label="Created"
                                value={new Date(
                                    mergedProject.createdAt
                                ).toLocaleDateString()}
                            />
                        </InfoSection>

                        {/* Location Details */}
                        <InfoSection title="Location" icon="location-on">
                            <InfoRow
                                label="Address"
                                value={
                                    mergedProject.location?.address ||
                                    'Not specified'
                                }
                            />
                            <InfoRow
                                label="City"
                                value={
                                    mergedProject.location?.city ||
                                    'Not specified'
                                }
                            />
                            <InfoRow
                                label="State"
                                value={
                                    mergedProject.location?.state ||
                                    'Not specified'
                                }
                            />
                            <InfoRow
                                label="Pincode"
                                value={
                                    mergedProject.location?.pincode ||
                                    'Not specified'
                                }
                            />
                            {mergedProject.location?.plotSizeSqFt && (
                                <InfoRow
                                    label="Plot Size"
                                    value={`${mergedProject.location.plotSizeSqFt} sq ft`}
                                />
                            )}
                        </InfoSection>

                        {/* Budget Information */}
                        <InfoSection title="Budget" icon="attach-money">
                            <InfoRow
                                label="Budget Range"
                                value={formatBudget(mergedProject.budget)}
                            />
                        </InfoSection>

                        {/* Design Preferences */}
                        {mergedProject.designPreferences && (
                            <InfoSection title="Design Preferences" icon="home">
                                {mergedProject.designPreferences.floors && (
                                    <InfoRow
                                        label="Floors"
                                        value={mergedProject.designPreferences.floors.toString()}
                                    />
                                )}
                                {mergedProject.designPreferences.bedrooms && (
                                    <InfoRow
                                        label="Bedrooms"
                                        value={mergedProject.designPreferences.bedrooms.toString()}
                                    />
                                )}
                                {mergedProject.designPreferences.bathrooms && (
                                    <InfoRow
                                        label="Bathrooms"
                                        value={mergedProject.designPreferences.bathrooms.toString()}
                                    />
                                )}
                                <InfoRow
                                    label="Parking Required"
                                    value={
                                        mergedProject.designPreferences
                                            .parkingRequired
                                            ? 'Yes'
                                            : 'No'
                                    }
                                />
                                <InfoRow
                                    label="Garden Required"
                                    value={
                                        mergedProject.designPreferences
                                            .gardenRequired
                                            ? 'Yes'
                                            : 'No'
                                    }
                                />
                                <InfoRow
                                    label="Vastu Compliance"
                                    value={
                                        mergedProject.designPreferences
                                            .vastuCompliance
                                            ? 'Yes'
                                            : 'No'
                                    }
                                />
                            </InfoSection>
                        )}

                        {/* Additional Details */}
                        {(mergedProject.specialInstructions ||
                            mergedProject.additionalFacilities?.length > 0) && (
                            <InfoSection
                                title="Additional Details"
                                icon="description"
                            >
                                {mergedProject.additionalFacilities?.length >
                                    0 && (
                                    <InfoRow
                                        label="Additional Facilities"
                                        value={mergedProject.additionalFacilities.join(
                                            ', '
                                        )}
                                    />
                                )}
                                {mergedProject.specialInstructions && (
                                    <InfoRow
                                        label="Special Instructions"
                                        value={
                                            mergedProject.specialInstructions
                                        }
                                    />
                                )}
                            </InfoSection>
                        )}

                        {/* Hired Professionals */}
                        <InfoSection title="Team" icon="group">
                            <InfoRow
                                label="Broker Assistance"
                                value={
                                    mergedProject.brokerAssistanceRequired
                                        ? 'Required'
                                        : 'Not Required'
                                }
                            />
                            <InfoRow
                                label="Broker Status"
                                value={
                                    mergedProject.brokerId
                                        ? 'Hired'
                                        : 'Not Hired'
                                }
                            />
                            <InfoRow
                                label="Contractor Status"
                                value={
                                    mergedProject.contractorId
                                        ? 'Hired'
                                        : 'Not Hired'
                                }
                            />
                        </InfoSection>

                        {/* Progress Tracking - Show for all project participants */}
                        <ProgressTracker
                            projectId={id}
                            project={mergedProject}
                        />

                        {/* Project Communication - Show if contractor or broker is assigned */}
                        {(mergedProject.contractorId ||
                            mergedProject.brokerId) && (
                            <ProjectChat
                                projectId={id}
                                project={mergedProject}
                            />
                        )}

                        {/* Action Buttons */}
                        <View style={styles.actionsContainer}>
                            {/* Show Hire Broker button only if broker assistance is required and broker is not hired yet */}
                            {mergedProject.brokerAssistanceRequired &&
                                !mergedProject.brokerId && (
                                    <TouchableOpacity
                                        style={[
                                            styles.actionButton,
                                            { borderColor: theme.ACCENT },
                                        ]}
                                        onPress={handleHireBroker}
                                        activeOpacity={0.8}
                                    >
                                        <LinearGradient
                                            colors={[
                                                theme.PRIMARY,
                                                theme.SECONDARY,
                                            ]}
                                            start={{ x: 0, y: 0 }}
                                            end={{ x: 1, y: 0 }}
                                            style={styles.actionButtonGradient}
                                        >
                                            <MaterialIcons
                                                name="business"
                                                size={20}
                                                color={theme.WHITE}
                                            />
                                            <Text
                                                style={[
                                                    styles.actionButtonText,
                                                    { color: theme.WHITE },
                                                ]}
                                            >
                                                Hire Broker
                                            </Text>
                                        </LinearGradient>
                                    </TouchableOpacity>
                                )}

                            {/* Show Hire Contractor button only if:
                                1. Contractor is not hired yet AND
                                2. Either broker assistance is not required OR broker is already hired */}
                            {!mergedProject.contractorId &&
                                (!mergedProject.brokerAssistanceRequired ||
                                    mergedProject.brokerId) && (
                                    <TouchableOpacity
                                        style={[
                                            styles.actionButton,
                                            { borderColor: theme.ACCENT },
                                        ]}
                                        onPress={handleHireContractor}
                                        activeOpacity={0.8}
                                    >
                                        <LinearGradient
                                            colors={[
                                                theme.PRIMARY,
                                                theme.SECONDARY,
                                            ]}
                                            start={{ x: 0, y: 0 }}
                                            end={{ x: 1, y: 0 }}
                                            style={styles.actionButtonGradient}
                                        >
                                            <MaterialIcons
                                                name="build"
                                                size={20}
                                                color={theme.WHITE}
                                            />
                                            <Text
                                                style={[
                                                    styles.actionButtonText,
                                                    { color: theme.WHITE },
                                                ]}
                                            >
                                                Hire Contractor
                                            </Text>
                                        </LinearGradient>
                                    </TouchableOpacity>
                                )}
                        </View>
                    </Animated.View>
                </ScrollView>
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    safeArea: {
        flex: 1,
    },
    header: {
        paddingTop: 10,
        paddingBottom: 15,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#fff',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 16,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingTop: 20,
        paddingBottom: 100,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        fontSize: 16,
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        gap: 16,
    },
    errorText: {
        fontSize: 18,
        fontWeight: '600',
    },
    section: {
        borderRadius: 20,
        padding: 20,
        marginHorizontal: 20,
        marginBottom: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    sectionIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    sectionContent: {
        gap: 12,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 8,
    },
    infoContent: {
        flex: 1,
    },
    infoLabel: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 2,
    },
    infoValue: {
        fontSize: 14,
        lineHeight: 20,
    },
    actionsContainer: {
        paddingHorizontal: 20,
        gap: 16,
        marginTop: 20,
    },
    actionButton: {
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    disabledButton: {
        opacity: 0.6,
    },
    actionButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 8,
        paddingVertical: 14,
    },
    actionButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
});
