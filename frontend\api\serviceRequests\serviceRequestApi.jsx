import { privateAPIClient } from '../index';

export const sendServiceRequest = async (serviceRequestData) => {
    try {
        const { projectId, recipientId, recipientType } = serviceRequestData;
        const response = await privateAPIClient.post(
            `/site-service/api/v1/projects/${projectId}/service-requests`,
            {
                recipientId,
                recipientType,
            }
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to send service request'
        );
    }
};

export const fetchServiceRequests = async () => {
    try {
        const response = await privateAPIClient.get(
            '/site-service/api/v1/service-requests'
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to fetch service requests'
        );
    }
};

export const acceptServiceRequest = async (projectId) => {
    try {
        const response = await privateAPIClient.patch(
            `/site-service/api/v1/service-request/${projectId}`,
            {
                status: 'accepted',
            }
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to accept service request'
        );
    }
};

export const rejectServiceRequest = async (projectId) => {
    try {
        const response = await privateAPIClient.patch(
            `/site-service/api/v1/service-request/${projectId}`,
            {
                status: 'rejected',
            }
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to reject service request'
        );
    }
};
