import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    Image,
    SafeAreaView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const SavedProperties = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [selectedTab, setSelectedTab] = useState('all');

    // Mock saved properties data
    const [savedProperties, setSavedProperties] = useState([
        {
            id: 1,
            title: 'Luxury Villa in Green Valley',
            location: 'Sector 15, Gurgaon',
            price: '₹2.5 Cr',
            area: '3500 sq ft',
            bedrooms: 4,
            type: 'Villa',
            image: require('../../assets/images/image1.png'),
            savedDate: '2024-01-15',
            broker: 'Elite Properties',
            rating: 4.8,
            features: ['Swimming Pool', 'Garden', 'Parking'],
        },
        {
            id: 2,
            title: 'Modern 3BHK Apartment',
            location: 'Bandra West, Mumbai',
            price: '₹1.8 Cr',
            area: '1200 sq ft',
            bedrooms: 3,
            type: 'Apartment',
            image: require('../../assets/images/image2.png'),
            savedDate: '2024-01-12',
            broker: 'Prime Realty',
            rating: 4.6,
            features: ['Gym', 'Security', 'Lift'],
        },
        {
            id: 3,
            title: 'Commercial Plot',
            location: 'Electronic City, Bangalore',
            price: '₹85 Lakh',
            area: '2000 sq ft',
            bedrooms: 0,
            type: 'Plot',
            image: require('../../assets/images/image3.png'),
            savedDate: '2024-01-10',
            broker: 'Land Deals',
            rating: 4.5,
            features: ['Corner Plot', 'Main Road', 'Clear Title'],
        },
    ]);

    const tabs = [
        { id: 'all', name: 'All Properties', count: savedProperties.length },
        {
            id: 'Villa',
            name: 'Villas',
            count: savedProperties.filter((p) => p.type === 'Villa').length,
        },
        {
            id: 'Apartment',
            name: 'Apartments',
            count: savedProperties.filter((p) => p.type === 'Apartment').length,
        },
        {
            id: 'Plot',
            name: 'Plots',
            count: savedProperties.filter((p) => p.type === 'Plot').length,
        },
    ];

    const filteredProperties = savedProperties.filter((property) => {
        if (selectedTab === 'all') return true;
        return property.type === selectedTab;
    });

    const removeFromSaved = (propertyId) => {
        setSavedProperties((prev) => prev.filter((p) => p.id !== propertyId));
        showToast('info', 'Listing removed from saved list.');
    };

    const renderProperty = (property) => (
        <TouchableOpacity
            key={property.id}
            style={[
                styles.propertyCard,
                { backgroundColor: theme.CARD, shadowColor: theme.PRIMARY },
            ]}
            onPress={() =>
                router.push(`/Properties/LandDetails?id=${property.id}`)
            }
        >
            <View style={styles.imageContainer}>
                <Image source={property.image} style={styles.propertyImage} />
                <View style={styles.imageOverlay}>
                    <TouchableOpacity
                        style={[
                            styles.actionButton,
                            { backgroundColor: theme.PRIMARY + '60' },
                        ]}
                        onPress={() => removeFromSaved(property.id)}
                    >
                        <Ionicons
                            name="bookmark"
                            size={20}
                            color={theme.PRIMARY}
                        />
                    </TouchableOpacity>
                </View>
                <View
                    style={[styles.typeTag, { backgroundColor: theme.PRIMARY }]}
                >
                    <Text style={styles.typeText}>{property.type}</Text>
                </View>
            </View>

            <View style={styles.propertyInfo}>
                <Text
                    style={[
                        styles.propertyTitle,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                >
                    {property.title}
                </Text>

                <View style={styles.locationRow}>
                    <Ionicons
                        name="location"
                        size={16}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.location,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {property.location}
                    </Text>
                </View>

                <View style={styles.detailsRow}>
                    <Text style={[styles.price, { color: theme.PRIMARY }]}>
                        {property.price}
                    </Text>
                    {property.bedrooms > 0 && (
                        <View style={styles.roomsRow}>
                            <View style={styles.roomInfo}>
                                <Ionicons
                                    name="bed"
                                    size={16}
                                    color={theme.TEXT_SECONDARY}
                                />
                                <Text
                                    style={[
                                        styles.roomText,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    {property.bedrooms} BHK
                                </Text>
                            </View>
                            <Text
                                style={[
                                    styles.area,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                {property.area}
                            </Text>
                        </View>
                    )}
                </View>

                <View style={styles.featuresContainer}>
                    {property.features.slice(0, 2).map((feature, index) => (
                        <View
                            key={index}
                            style={[
                                styles.featureTag,
                                { backgroundColor: theme.PRIMARY + '20' },
                            ]}
                        >
                            <Text
                                style={[
                                    styles.featureText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                {feature}
                            </Text>
                        </View>
                    ))}
                    {property.features.length > 2 && (
                        <Text
                            style={[
                                styles.moreFeatures,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            +{property.features.length - 2} more
                        </Text>
                    )}
                </View>

                <View style={styles.bottomRow}>
                    <View style={styles.brokerInfo}>
                        <Text
                            style={[
                                styles.brokerText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            by {property.broker}
                        </Text>
                        <View style={styles.ratingRow}>
                            <Ionicons name="star" size={12} color="#FFD700" />
                            <Text
                                style={[
                                    styles.rating,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                {property.rating}
                            </Text>
                        </View>
                    </View>
                    <Text
                        style={[
                            styles.savedDate,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Saved{' '}
                        {new Date(property.savedDate).toLocaleDateString()}
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color="#fff" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Saved Properties</Text>
                    <TouchableOpacity
                        onPress={() =>
                            router.push('/Properties/PropertyFilters')
                        }
                    >
                        <Ionicons name="filter" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>
            </LinearGradient>
            <View style={styles.tabsContainer}>
                {/* Tabs */}
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.tabsContent}
                >
                    {tabs.map((tab) => (
                        <TouchableOpacity
                            key={tab.id}
                            style={[
                                styles.tab,
                                {
                                    backgroundColor:
                                        selectedTab === tab.id
                                            ? theme.PRIMARY + '66'
                                            : theme.BACKGROUND,
                                    borderColor:
                                        selectedTab === tab.id
                                            ? theme.PRIMARY
                                            : theme.GRAY_LIGHT,
                                },
                            ]}
                            onPress={() => setSelectedTab(tab.id)}
                        >
                            <Text
                                style={[
                                    styles.tabText,
                                    {
                                        color:
                                            selectedTab === tab.id
                                                ? theme.PRIMARY
                                                : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                {tab.name}
                            </Text>
                            {tab.count > 0 && (
                                <View
                                    style={[
                                        styles.tabBadge,
                                        {
                                            backgroundColor:
                                                selectedTab === tab.id
                                                    ? theme.PRIMARY
                                                    : theme.PRIMARY + '33',
                                        },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.tabBadgeText,
                                            {
                                                color:
                                                    selectedTab === tab.id
                                                        ? theme.WHITE
                                                        : theme.PRIMARY,
                                            },
                                        ]}
                                    >
                                        {tab.count}
                                    </Text>
                                </View>
                            )}
                        </TouchableOpacity>
                    ))}
                </ScrollView>
            </View>

            {/* Properties List */}
            <ScrollView
                style={styles.propertiesList}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.propertiesContent}
            >
                {filteredProperties.length > 0 ? (
                    filteredProperties.map(renderProperty)
                ) : (
                    <View style={styles.emptyState}>
                        <Ionicons
                            name="bookmark-outline"
                            size={60}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.emptyStateText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            No saved properties found
                        </Text>
                        <Text
                            style={[
                                styles.emptyStateSubtext,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Start exploring properties and save your favorites
                        </Text>
                        <TouchableOpacity
                            style={[
                                styles.exploreButton,
                                { backgroundColor: theme.PRIMARY },
                            ]}
                            onPress={() => router.push('/(tabs)/Listings')}
                        >
                            <Text style={styles.exploreButtonText}>
                                Explore Properties
                            </Text>
                        </TouchableOpacity>
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 10,
        paddingBottom: 10,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    tabsContainer: {
        marginTop: 4,
        marginBottom: 4,
    },
    tabsContent: {
        paddingHorizontal: 10,
        alignItems: 'center',
    },
    tab: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 25,
        borderWidth: 1,
        marginRight: 8,
        flexShrink: 0, // Prevent shrinking
        minWidth: 'auto', // Allow natural width
    },
    tabText: {
        fontSize: 12,
        fontWeight: '600',
    },
    tabBadge: {
        marginLeft: 6,
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 10,
        minWidth: 20,
        alignItems: 'center',
    },
    tabBadgeText: {
        fontSize: 12,
        fontWeight: 'bold',
    },
    propertiesList: {
        flex: 1,
        marginBottom: 60,
    },
    propertiesContent: {
        marginTop: 4,
        paddingHorizontal: 20,
    },
    propertyCard: {
        borderRadius: 10,
        marginBottom: 16,
        elevation: 3,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        overflow: 'hidden',
    },
    imageContainer: {
        position: 'relative',
    },
    propertyImage: {
        width: '100%',
        height: 150,
    },
    imageOverlay: {
        position: 'absolute',
        top: 12,
        right: 12,
        flexDirection: 'row',
    },
    actionButton: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 8,
    },
    typeTag: {
        position: 'absolute',
        bottom: 12,
        left: 12,
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
    },
    typeText: {
        fontSize: 10,
        fontWeight: 'bold',
        color: '#fff',
    },
    propertyInfo: {
        padding: 8,
    },
    propertyTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    locationRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
    },
    location: {
        fontSize: 12,
        marginLeft: 4,
    },
    detailsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 4,
    },
    price: {
        fontSize: 14,
        fontWeight: 'bold',
    },
    area: {
        fontSize: 12,
    },
    roomsRow: {
        flexDirection: 'row',
        marginBottom: 4,
    },
    roomInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 16,
    },
    roomText: {
        fontSize: 12,
        marginLeft: 4,
    },
    featuresContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    featureTag: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginRight: 8,
    },
    featureText: {
        fontSize: 10,
        fontWeight: '600',
    },
    moreFeatures: {
        fontSize: 10,
    },
    bottomRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    brokerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    brokerText: {
        fontSize: 10,
        marginRight: 8,
    },
    ratingRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rating: {
        fontSize: 10,
        marginLeft: 2,
    },
    savedDate: {
        fontSize: 10,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyStateText: {
        fontSize: 14,
        fontWeight: 'bold',
        marginTop: 16,
        textAlign: 'center',
    },
    emptyStateSubtext: {
        fontSize: 12,
        marginTop: 8,
        textAlign: 'center',
        marginBottom: 24,
    },
    exploreButton: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 10,
    },
    exploreButtonText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: '#fff',
    },
});

export default SavedProperties;
