import React from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Pressable,
    ScrollView,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import ModalDatePicker from 'react-native-modal-datetime-picker';

const CreateProjectStep1 = ({
    formik,
    theme,
    setStep,
    projectTypes,
    constructionTypes,
    showStartDatePicker,
    setShowStartDatePicker,
    showCompletionDatePicker,
    setShowCompletionDatePicker,
    styles,
}) => {
    const { values, errors, touched, handleChange, handleBlur, setFieldValue } =
        formik;

    return (
        <>
            <View style={styles.sectionHeader}>
                <Ionicons
                    name="briefcase-outline"
                    size={20}
                    color={theme.PRIMARY}
                />
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    Project Details
                </Text>
            </View>
            {/* Project Name */}
            <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                    Project name
                    <Text style={{ color: theme.ERROR }}>*</Text>
                </Text>
                <View
                    style={[
                        styles.inputContainer,
                        {
                            backgroundColor: theme.INPUT_BACKGROUND,
                            borderColor:
                                errors.projectName && touched.projectName
                                    ? theme.ERROR
                                    : theme.INPUT_BORDER,
                        },
                        errors.projectName &&
                            touched.projectName &&
                            styles.inputError,
                    ]}
                >
                    <Ionicons
                        name="home-outline"
                        size={20}
                        color={
                            errors.projectName && touched.projectName
                                ? theme.ERROR
                                : theme.PRIMARY
                        }
                        style={styles.inputIcon}
                    />
                    <TextInput
                        style={[
                            styles.input,
                            {
                                color: theme.TEXT_PRIMARY,
                                flex: 1,
                            },
                        ]}
                        placeholder="Enter project name"
                        placeholderTextColor={theme.TEXT_PLACEHOLDER}
                        value={values.projectName}
                        onChangeText={handleChange('projectName')}
                        onBlur={handleBlur('projectName')}
                    />
                </View>
                {errors.projectName && touched.projectName && (
                    <Text style={[styles.errorText, { color: theme.ERROR }]}>
                        {errors.projectName}
                    </Text>
                )}
            </View>

            {/* Project Type */}
            <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                    Project type
                    <Text style={{ color: theme.ERROR }}>*</Text>
                </Text>
                <View
                    style={[
                        styles.inputContainer,
                        {
                            backgroundColor: theme.INPUT_BACKGROUND,
                            borderColor:
                                errors.projectType && touched.projectType
                                    ? theme.ERROR
                                    : theme.INPUT_BORDER,
                        },
                        errors.projectType &&
                            touched.projectType &&
                            styles.inputError,
                    ]}
                >
                    <Ionicons
                        name="business-outline"
                        size={20}
                        color={
                            errors.projectType && touched.projectType
                                ? theme.ERROR
                                : theme.PRIMARY
                        }
                        style={styles.inputIcon}
                    />
                    <Picker
                        selectedValue={values.projectType}
                        onValueChange={(value) =>
                            setFieldValue('projectType', value)
                        }
                        dropdownIconColor={theme.PRIMARY}
                        style={[
                            styles.picker,
                            {
                                color: values.projectType
                                    ? theme.TEXT_PRIMARY
                                    : theme.TEXT_PLACEHOLDER,
                            },
                        ]}
                        accessibilityLabel="Select project type"
                        testID="project-type-picker"
                    >
                        <Picker.Item
                            label="Select Project type"
                            value=""
                            style={{ fontSize: 14 }}
                        />
                        {projectTypes.map((type) => (
                            <Picker.Item
                                key={type}
                                label={type}
                                value={type}
                                style={{ fontSize: 14 }}
                            />
                        ))}
                    </Picker>
                </View>
                {errors.projectType && touched.projectType && (
                    <Text style={[styles.errorText, { color: theme.ERROR }]}>
                        {errors.projectType}
                    </Text>
                )}
            </View>

            {/* Construction Type */}
            <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                    Construction type
                    <Text style={{ color: theme.ERROR }}>*</Text>
                </Text>
                <View
                    style={[
                        styles.inputContainer,
                        {
                            backgroundColor: theme.INPUT_BACKGROUND,
                            borderColor:
                                errors.constructionType &&
                                touched.constructionType
                                    ? theme.ERROR
                                    : theme.INPUT_BORDER,
                        },
                        errors.constructionType &&
                            touched.constructionType &&
                            styles.inputError,
                    ]}
                >
                    <Ionicons
                        name="construct-outline"
                        size={20}
                        color={
                            errors.constructionType && touched.constructionType
                                ? theme.ERROR
                                : theme.PRIMARY
                        }
                        style={styles.inputIcon}
                    />
                    <Picker
                        selectedValue={values.constructionType}
                        onValueChange={(value) =>
                            setFieldValue('constructionType', value)
                        }
                        dropdownIconColor={theme.PRIMARY}
                        style={[
                            styles.picker,
                            {
                                color: values.constructionType
                                    ? theme.TEXT_PRIMARY
                                    : theme.TEXT_PLACEHOLDER,
                            },
                        ]}
                        accessibilityLabel="Select construction type"
                        testID="construction-type-picker"
                    >
                        <Picker.Item
                            label="Select Construction type"
                            value=""
                            style={{ fontSize: 14 }}
                        />
                        {constructionTypes.map((type) => (
                            <Picker.Item
                                key={type}
                                label={type}
                                value={type}
                                style={{ fontSize: 14 }}
                            />
                        ))}
                    </Picker>
                </View>
                {errors.constructionType && touched.constructionType && (
                    <Text style={[styles.errorText, { color: theme.ERROR }]}>
                        {errors.constructionType}
                    </Text>
                )}
            </View>

            {/* Location Details Section */}
            <View style={styles.sectionHeader}>
                <MaterialIcons
                    name="location-on"
                    size={20}
                    color={theme.PRIMARY}
                />
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    Location Details
                </Text>
            </View>

            {/* Address */}
            <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                Address
                <Text style={{ color: theme.ERROR }}>*</Text>
            </Text>
            <View
                style={[
                    styles.inputContainer,
                    styles.addressInputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor:
                            errors.address && touched.address
                                ? theme.ERROR
                                : theme.INPUT_BORDER,
                    },
                    errors.address && touched.address && styles.inputError,
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={
                        errors.address && touched.address
                            ? theme.ERROR
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Enter full address"
                    value={values.address}
                    onChangeText={handleChange('address')}
                    onBlur={handleBlur('address')}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    multiline
                    accessibilityLabel="Enter address"
                    testID="address-input"
                />
                {values.address.length > 0 && (
                    <TouchableOpacity
                        onPress={() => setFieldValue('address', '')}
                        style={styles.clearButton}
                    >
                        <Ionicons name="close-circle" size={20} color="#999" />
                    </TouchableOpacity>
                )}
            </View>
            {errors.address && touched.address && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.address}
                </Text>
            )}

            {/* City and State Row */}
            <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1, marginRight: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        City
                        <Text style={{ color: theme.ERROR }}>*</Text>
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.city && touched.city
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.city && touched.city && styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="business-outline"
                            size={20}
                            color={
                                errors.city && touched.city
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            placeholder="Enter city"
                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                            value={values.city}
                            onChangeText={handleChange('city')}
                            onBlur={handleBlur('city')}
                        />
                    </View>
                    {errors.city && touched.city && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.city}
                        </Text>
                    )}
                </View>

                <View style={[styles.inputGroup, { flex: 1, marginLeft: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        State
                        <Text style={{ color: theme.ERROR }}>*</Text>
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.state && touched.state
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.state && touched.state && styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="map-outline"
                            size={20}
                            color={
                                errors.state && touched.state
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            placeholder="Enter state"
                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                            value={values.state}
                            onChangeText={handleChange('state')}
                            onBlur={handleBlur('state')}
                        />
                    </View>
                    {errors.state && touched.state && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.state}
                        </Text>
                    )}
                </View>
            </View>

            {/* Pincode and Plot Size Row */}
            <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1, marginRight: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Pincode
                        <Text style={{ color: theme.ERROR }}>*</Text>
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.pincode && touched.pincode
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.pincode &&
                                touched.pincode &&
                                styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="mail-outline"
                            size={20}
                            color={
                                errors.pincode && touched.pincode
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            placeholder="Enter pincode"
                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                            value={values.pincode}
                            onChangeText={handleChange('pincode')}
                            onBlur={handleBlur('pincode')}
                            keyboardType="numeric"
                            maxLength={6}
                        />
                    </View>
                    {errors.pincode && touched.pincode && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.pincode}
                        </Text>
                    )}
                </View>

                <View style={[styles.inputGroup, { flex: 1, marginLeft: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Plot Size (sq ft)
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.plotSizeSqFt && touched.plotSizeSqFt
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.plotSizeSqFt &&
                                touched.plotSizeSqFt &&
                                styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="resize-outline"
                            size={20}
                            color={
                                errors.plotSizeSqFt && touched.plotSizeSqFt
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            placeholder="Plot size"
                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                            value={values.plotSizeSqFt}
                            onChangeText={handleChange('plotSizeSqFt')}
                            onBlur={handleBlur('plotSizeSqFt')}
                            keyboardType="numeric"
                        />
                    </View>
                    {errors.plotSizeSqFt && touched.plotSizeSqFt && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.plotSizeSqFt}
                        </Text>
                    )}
                </View>
            </View>

            {/* Project Timeline Section */}
            <View style={styles.sectionHeader}>
                <MaterialIcons
                    name="schedule"
                    size={20}
                    color={theme.PRIMARY}
                />
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    Project Timeline
                </Text>
            </View>

            {/* Expected Start Date */}
            <View style={[styles.inputGroup]}>
                <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                    Expected Start Date
                </Text>
                <Pressable
                    onPress={() => setShowStartDatePicker(true)}
                    style={[
                        styles.inputContainer,
                        {
                            backgroundColor: theme.INPUT_BACKGROUND,
                            borderColor:
                                errors.expectedStartDate &&
                                touched.expectedStartDate
                                    ? theme.ERROR
                                    : theme.INPUT_BORDER,
                        },
                        errors.expectedStartDate &&
                            touched.expectedStartDate &&
                            styles.inputError,
                    ]}
                >
                    <Ionicons
                        name="calendar-outline"
                        size={20}
                        color={
                            errors.expectedStartDate &&
                            touched.expectedStartDate
                                ? theme.ERROR
                                : theme.PRIMARY
                        }
                        style={styles.inputIcon}
                    />
                    <Text
                        style={[
                            styles.input,
                            {
                                color: values.expectedStartDate
                                    ? theme.TEXT_PRIMARY
                                    : theme.TEXT_PLACEHOLDER,
                            },
                        ]}
                    >
                        {values.expectedStartDate
                            ? new Date(
                                  values.expectedStartDate
                              ).toLocaleDateString()
                            : 'Expected Start Date'}
                    </Text>
                </Pressable>
                <ModalDatePicker
                    isVisible={showStartDatePicker}
                    mode="date"
                    date={
                        values.expectedStartDate
                            ? new Date(values.expectedStartDate)
                            : new Date()
                    }
                    onConfirm={(date) => {
                        setFieldValue('expectedStartDate', date);
                        setShowStartDatePicker(false);
                    }}
                    onCancel={() => setShowStartDatePicker(false)}
                    minimumDate={new Date()}
                    testID="startDatePicker"
                />
                {errors.expectedStartDate && touched.expectedStartDate && (
                    <Text style={[styles.errorText, { color: theme.ERROR }]}>
                        {errors.expectedStartDate}
                    </Text>
                )}
            </View>

            {/* Expected Completion Date */}
            <View style={[styles.inputGroup, { flex: 1 }]}>
                <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                    Expected Completion Date
                </Text>
                <Pressable
                    onPress={() => setShowCompletionDatePicker(true)}
                    style={[
                        styles.inputContainer,
                        {
                            backgroundColor: theme.INPUT_BACKGROUND,
                            borderColor:
                                errors.expectedCompletionDate &&
                                touched.expectedCompletionDate
                                    ? theme.ERROR
                                    : theme.INPUT_BORDER,
                        },
                        errors.expectedCompletionDate &&
                            touched.expectedCompletionDate &&
                            styles.inputError,
                    ]}
                >
                    <Ionicons
                        name="calendar-outline"
                        size={20}
                        color={
                            errors.expectedCompletionDate &&
                            touched.expectedCompletionDate
                                ? theme.ERROR
                                : theme.PRIMARY
                        }
                        style={styles.inputIcon}
                    />
                    <Text
                        style={[
                            styles.input,
                            {
                                color: values.expectedCompletionDate
                                    ? theme.TEXT_PRIMARY
                                    : theme.TEXT_PLACEHOLDER,
                            },
                        ]}
                    >
                        {values.expectedCompletionDate
                            ? new Date(
                                  values.expectedCompletionDate
                              ).toLocaleDateString()
                            : 'Expected Completion Date'}
                    </Text>
                </Pressable>
                <ModalDatePicker
                    isVisible={showCompletionDatePicker}
                    mode="date"
                    date={
                        values.expectedCompletionDate
                            ? new Date(values.expectedCompletionDate)
                            : new Date()
                    }
                    onConfirm={(date) => {
                        setFieldValue('expectedCompletionDate', date);
                        setShowCompletionDatePicker(false);
                    }}
                    onCancel={() => setShowCompletionDatePicker(false)}
                    minimumDate={
                        values.expectedStartDate
                            ? new Date(values.expectedStartDate)
                            : new Date()
                    }
                    testID="completionDatePicker"
                />
                {errors.expectedCompletionDate &&
                    touched.expectedCompletionDate && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.expectedCompletionDate}
                        </Text>
                    )}
            </View>

            {/* Budget Details Section */}
            <View style={styles.sectionHeader}>
                <Text
                    style={{
                        fontSize: 20,
                        color: theme.PRIMARY,
                    }}
                >
                    ₹
                </Text>
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    Budget Details
                </Text>
            </View>

            {/* Budget Range Row */}
            <View style={styles.row}>
                {/* Min Budget */}
                <View style={[styles.inputGroup, { flex: 1, marginRight: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Min Budget
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.minBudget && touched.minBudget
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.minBudget &&
                                touched.minBudget &&
                                styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="wallet-outline"
                            size={20}
                            color={
                                errors.minBudget && touched.minBudget
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            placeholder="Minimum"
                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                            value={values.minBudget}
                            onChangeText={handleChange('minBudget')}
                            onBlur={handleBlur('minBudget')}
                            keyboardType="numeric"
                        />
                    </View>
                    {errors.minBudget && touched.minBudget && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.minBudget}
                        </Text>
                    )}
                </View>

                {/* Max Budget */}
                <View style={[styles.inputGroup, { flex: 1, marginLeft: 4 }]}>
                    <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                        Max Budget
                    </Text>
                    <View
                        style={[
                            styles.inputContainer,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                borderColor:
                                    errors.maxBudget && touched.maxBudget
                                        ? theme.ERROR
                                        : theme.INPUT_BORDER,
                            },
                            errors.maxBudget &&
                                touched.maxBudget &&
                                styles.inputError,
                        ]}
                    >
                        <Ionicons
                            name="wallet-outline"
                            size={20}
                            color={
                                errors.maxBudget && touched.maxBudget
                                    ? theme.ERROR
                                    : theme.PRIMARY
                            }
                            style={styles.inputIcon}
                        />
                        <TextInput
                            style={[
                                styles.input,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            placeholder="Maximum"
                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                            value={values.maxBudget}
                            onChangeText={handleChange('maxBudget')}
                            onBlur={handleBlur('maxBudget')}
                            keyboardType="numeric"
                        />
                    </View>
                    {errors.maxBudget && touched.maxBudget && (
                        <Text
                            style={[styles.errorText, { color: theme.ERROR }]}
                        >
                            {errors.maxBudget}
                        </Text>
                    )}
                </View>
            </View>

            {/* Next Button */}
            <TouchableOpacity
                style={[
                    styles.submitButton,
                    {
                        borderColor: theme.ACCENT,
                        marginTop: 24,
                    },
                ]}
                onPress={() => setStep(2)}
                accessibilityLabel="Proceed to step 2"
                accessibilityRole="button"
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.submitButtonGradient}
                >
                    <Text
                        style={[
                            styles.submitButtonText,
                            { color: theme.WHITE },
                        ]}
                    >
                        Next
                    </Text>
                </LinearGradient>
            </TouchableOpacity>
        </>
    );
};

export default CreateProjectStep1;
