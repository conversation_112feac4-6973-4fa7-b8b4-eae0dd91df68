import React, { useState, useContext, useEffect, useRef } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    TextInput,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    Alert,
    ActivityIndicator,
    Dimensions,
    Animated,
    Easing,
    Image,
    Pressable,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import ModalDatePicker from 'react-native-modal-datetime-picker';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    fetchProjectDetails,
    updateProject,
} from '../../api/projects/projectApi.jsx';
import Toast from 'react-native-toast-message';
import EditProjectStep1 from './EditProjectStep1';
import EditProjectStep2 from './EditProjectStep2';
import StepIndicator from './components/StepIndicator';

const { width, height } = Dimensions.get('window');

const validationSchema = Yup.object().shape({
    projectName: Yup.string().required('Project name is required'),
    projectType: Yup.string().required('Project type is required'),
    constructionType: Yup.string().required('Construction type is required'),
    address: Yup.string().required('Address is required'),
    city: Yup.string().required('City is required'),
    state: Yup.string().required('State is required'),
    pincode: Yup.string().required('Pincode is required'),
    plotSizeSqFt: Yup.number().positive('Plot size must be positive'),
    minBudget: Yup.number().positive('Minimum budget must be positive'),
    maxBudget: Yup.number()
        .positive('Maximum budget must be positive')
        .test(
            'max-greater-than-min',
            'Maximum budget must be greater than minimum budget',
            function (value) {
                const { minBudget } = this.parent;
                return !minBudget || !value || value > minBudget;
            }
        ),
    floors: Yup.number().positive('Number of floors must be positive'),
    bedrooms: Yup.number().min(0, 'Number of bedrooms cannot be negative'),
    bathrooms: Yup.number().min(0, 'Number of bathrooms cannot be negative'),
    expectedStartDate: Yup.date().nullable(),
    expectedCompletionDate: Yup.date()
        .nullable()
        .test(
            'completion-after-start',
            'Completion date must be after start date',
            function (value) {
                const { expectedStartDate } = this.parent;
                return (
                    !expectedStartDate || !value || value > expectedStartDate
                );
            }
        ),
});

export default function EditProject() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const { id } = useLocalSearchParams();
    const queryClient = useQueryClient();

    // Step management
    const [currentStep, setCurrentStep] = useState(1);

    // Date picker states
    const [showStartDatePicker, setShowStartDatePicker] = useState(false);
    const [showCompletionDatePicker, setShowCompletionDatePicker] =
        useState(false);

    // Selected type states for dynamic updates
    const [selectedProjectType, setSelectedProjectType] = useState('');
    const [selectedConstructionType, setSelectedConstructionType] =
        useState('');

    // Animation refs
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    // Fetch project details
    const {
        data: projectData,
        isLoading: isLoadingProject,
        error: projectError,
    } = useQuery({
        queryKey: ['projectDetails', id],
        queryFn: () => fetchProjectDetails(id),
        enabled: !!id,
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to fetch project details',
            });
        },
    });

    const updateProjectMutation = useMutation({
        mutationFn: (projectData) => updateProject(id, projectData),
        onSuccess: () => {
            Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Project updated successfully!',
            });
            queryClient.invalidateQueries(['projectDetails', id]);
            queryClient.invalidateQueries(['userProjects']);
            router.back();
        },
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to update project',
            });
        },
    });

    const projectTypes = ['Residential', 'Commercial', 'Industrial', 'Other'];
    const constructionTypes = ['New Construction', 'Renovation', 'Extension'];

    // Create initial values from project data
    const getInitialValues = () => {
        if (!projectData?.project || !projectData?.requirement) {
            return {
                projectName: '',
                projectType: '',
                constructionType: '',
                address: '',
                city: '',
                state: '',
                pincode: '',
                plotSizeSqFt: '',
                expectedStartDate: '',
                expectedCompletionDate: '',
                minBudget: '',
                maxBudget: '',
                floors: '',
                bedrooms: '',
                bathrooms: '',
                parkingRequired: false,
                gardenRequired: false,
                vastuCompliance: false,
                brokerAssistanceRequired: false,
                specialInstructions: '',
                additionalFacilities: '',
            };
        }

        const { project, requirement } = projectData;

        return {
            projectName: project.projectName || '',
            projectType: requirement.projectType || '',
            constructionType: requirement.constructionType || '',
            address: requirement.location?.address || '',
            city: requirement.location?.city || '',
            state: requirement.location?.state || '',
            pincode: requirement.location?.pincode || '',
            plotSizeSqFt: requirement.location?.plotSizeSqFt?.toString() || '',
            expectedStartDate: requirement.expectedStartDate || '',
            expectedCompletionDate: requirement.expectedCompletionDate || '',
            minBudget: requirement.budget?.minBudget?.toString() || '',
            maxBudget: requirement.budget?.maxBudget?.toString() || '',
            floors: requirement.designPreferences?.floors?.toString() || '',
            bedrooms: requirement.designPreferences?.bedrooms?.toString() || '',
            bathrooms:
                requirement.designPreferences?.bathrooms?.toString() || '',
            parkingRequired:
                requirement.designPreferences?.parkingRequired || false,
            gardenRequired:
                requirement.designPreferences?.gardenRequired || false,
            vastuCompliance:
                requirement.designPreferences?.vastuCompliance || false,
            brokerAssistanceRequired:
                requirement.brokerAssistanceRequired || false,
            specialInstructions: requirement.specialInstructions || '',
            additionalFacilities: Array.isArray(
                requirement.additionalFacilities
            )
                ? requirement.additionalFacilities.join(', ')
                : '',
        };
    };

    // Update selected types when data loads
    useEffect(() => {
        if (projectData?.requirement) {
            setSelectedProjectType(projectData.requirement.projectType || '');
            setSelectedConstructionType(
                projectData.requirement.constructionType || ''
            );
        }
    }, [projectData]);

    // Initialize animations
    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const handleSubmit = (values) => {
        const projectUpdateData = {
            projectName: values.projectName,
            projectType: values.projectType,
            constructionType: values.constructionType,
            location: {
                address: values.address,
                city: values.city,
                state: values.state,
                pincode: values.pincode,
                plotSizeSqFt: values.plotSizeSqFt
                    ? parseInt(values.plotSizeSqFt)
                    : undefined,
            },
            expectedStartDate: values.expectedStartDate || undefined,
            expectedCompletionDate: values.expectedCompletionDate || undefined,
            budget: {
                minBudget: values.minBudget
                    ? parseInt(values.minBudget)
                    : undefined,
                maxBudget: values.maxBudget
                    ? parseInt(values.maxBudget)
                    : undefined,
            },
            designPreferences: {
                floors: values.floors ? parseInt(values.floors) : undefined,
                bedrooms: values.bedrooms
                    ? parseInt(values.bedrooms)
                    : undefined,
                bathrooms: values.bathrooms
                    ? parseInt(values.bathrooms)
                    : undefined,
                parkingRequired: values.parkingRequired,
                gardenRequired: values.gardenRequired,
                vastuCompliance: values.vastuCompliance,
            },
            brokerAssistanceRequired: values.brokerAssistanceRequired,
            specialInstructions: values.specialInstructions || undefined,
            additionalFacilities: values.additionalFacilities
                ? values.additionalFacilities
                      .split(',')
                      .map((f) => f.trim())
                      .filter((f) => f)
                : [],
        };

        updateProjectMutation.mutate(projectUpdateData);
    };

    if (isLoadingProject) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Loading project details...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (projectError) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                <View style={styles.contentContainer}>
                    <Text style={[styles.errorText, { color: theme.ERROR }]}>
                        Failed to load project details
                    </Text>
                    <TouchableOpacity
                        style={[
                            styles.retryButton,
                            { backgroundColor: theme.PRIMARY },
                        ]}
                        onPress={() => router.back()}
                    >
                        <Text style={styles.retryButtonText}>Go Back</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Background Pattern */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            <SafeAreaView style={styles.safeArea}>
                {/* Header */}
                <View style={styles.headerContent}>
                    <BackButton color={theme.WHITE} />
                </View>

                <Formik
                    initialValues={getInitialValues()}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    enableReinitialize={true}
                >
                    {({
                        values,
                        errors,
                        touched,
                        handleChange,
                        handleBlur,
                        handleSubmit,
                        setFieldValue,
                    }) => (
                        <ScrollView
                            style={styles.scrollView}
                            showsVerticalScrollIndicator={false}
                        >
                            <Animated.View
                                style={[
                                    styles.formContainer,
                                    {
                                        transform: [{ scale: scaleAnim }],
                                        opacity: fadeAnim,
                                        shadowColor: theme.SHADOW,
                                        backgroundColor: theme.CARD,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.title,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Edit Project
                                </Text>
                                <Text
                                    style={[
                                        styles.subtitle,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Update your project preferences to get
                                    better matches with site scouts and
                                    contractors.
                                </Text>

                                {/* Step Indicator */}
                                <StepIndicator
                                    currentStep={currentStep}
                                    totalSteps={2}
                                    theme={theme}
                                />

                                {/* Step 1 */}
                                {currentStep === 1 && (
                                    <EditProjectStep1
                                        formik={{
                                            values,
                                            errors,
                                            touched,
                                            handleChange,
                                            handleBlur,
                                            setFieldValue,
                                        }}
                                        theme={theme}
                                        setStep={setCurrentStep}
                                        projectTypes={projectTypes}
                                        constructionTypes={constructionTypes}
                                        showStartDatePicker={
                                            showStartDatePicker
                                        }
                                        setShowStartDatePicker={
                                            setShowStartDatePicker
                                        }
                                        showCompletionDatePicker={
                                            showCompletionDatePicker
                                        }
                                        setShowCompletionDatePicker={
                                            setShowCompletionDatePicker
                                        }
                                        styles={styles}
                                    />
                                )}

                                {/* Step 2 */}
                                {currentStep === 2 && (
                                    <EditProjectStep2
                                        formik={{
                                            values,
                                            errors,
                                            touched,
                                            handleChange,
                                            handleBlur,
                                            setFieldValue,
                                            handleSubmit,
                                        }}
                                        theme={theme}
                                        setStep={setCurrentStep}
                                        isSubmitting={
                                            updateProjectMutation.isPending
                                        }
                                        styles={styles}
                                    />
                                )}
                            </Animated.View>
                        </ScrollView>
                    )}
                </Formik>
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: { flex: 1 },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'center',
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: height * 0.1,
    },
    safeArea: { flex: 1 },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    scrollView: { flex: 1 },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        marginHorizontal: 20,
        alignSelf: 'center',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 14,
        marginBottom: 24,
        textAlign: 'center',
    },
    inputGroup: {
        marginBottom: 8,
    },
    label: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 8,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 4,
        paddingHorizontal: 10,
        height: 56,
    },
    addressInputContainer: {
        height: 80,
    },
    inputIcon: {
        marginRight: 12,
    },
    clearButton: {
        padding: 8,
    },
    input: {
        flex: 1,
        fontSize: 14,
    },
    inputError: {
        borderColor: 'red',
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10,
        marginBottom: 16,
        marginHorizontal: 50,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
    },
    sectionTitleContainer: {
        flex: 1,
        marginLeft: 8,
    },
    sectionSubtitle: {
        fontSize: 12,
        marginTop: 2,
        lineHeight: 16,
    },
    row: {
        flexDirection: 'row',
    },
    errorText: { fontSize: 12, marginTop: 4 },
    submitButton: {
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        marginBottom: 8,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 14,
        alignItems: 'center',
    },
    submitText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    submitButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    buttonContainer: {
        flexDirection: 'row',
        marginTop: 24,
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    preferencesContainer: {
        marginTop: 16,
        gap: 16,
    },
    preferenceItem: {
        backgroundColor: 'transparent',
        borderRadius: 12,
        padding: 16,
        borderWidth: 1,
    },
    preferenceHeader: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    preferenceIcon: {
        marginRight: 12,
        marginTop: 2,
    },
    preferenceLabelContainer: {
        flex: 1,
    },
    preferenceLabel: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    preferenceDescription: {
        fontSize: 12,
        lineHeight: 16,
    },
    toggleButtonContainer: {
        flexDirection: 'row',
        borderRadius: 8,
        overflow: 'hidden',
        borderWidth: 1,
        marginTop: 8,
    },
    toggleButton: {
        flex: 1,
        paddingVertical: 10,
        paddingHorizontal: 16,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 0,
    },
    toggleButtonLeft: {
        borderTopRightRadius: 0,
        borderBottomRightRadius: 0,
        borderRightWidth: 0.5,
    },
    toggleButtonRight: {
        borderTopLeftRadius: 0,
        borderBottomLeftRadius: 0,
        borderLeftWidth: 0.5,
    },
    toggleButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },
    picker: {
        flex: 1,
        height: 56,
        fontSize: 14,
    },
    capsuleScrollView: {
        marginTop: 8,
    },
    capsuleContainer: {
        paddingHorizontal: 4,
        gap: 8,
    },
    capsule: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        marginHorizontal: 4,
        minHeight: 36,
        justifyContent: 'center',
        alignItems: 'center',
    },
    capsuleText: {
        fontSize: 14,
        fontWeight: '500',
        textAlign: 'center',
    },
    additionalDetailsContainer: {
        marginTop: 16,
    },
    loadingText: {
        marginLeft: 8,
        fontSize: 16,
    },
    retryButton: {
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 8,
        marginTop: 16,
    },
    retryButtonText: {
        color: 'white',
        fontWeight: 'bold',
    },
});
