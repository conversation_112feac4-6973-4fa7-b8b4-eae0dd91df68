import React from 'react';
import { Text } from 'react-native';
import { useRouter } from 'expo-router';
import CategorySection from '../Shared/CategorySection';
import { useQuery } from '@tanstack/react-query';
import { fetchAllContractors } from '../../../api/contractor/contractorApi';

export default function Contractors() {
    const router = useRouter();
    const {
        data: contractors,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['allContractors'],
        queryFn: fetchAllContractors,
    });

    const handleContractorPress = (contractor) => {
        router.push({
            pathname: '/Profile/ContractorProfile',
            params: { contractorId: contractor.id },
        });
    };

    return isLoading ? (
        <Text>Loading...</Text>
    ) : isError ? (
        <Text>Error: {error.message}</Text>
    ) : (
        <CategorySection
            title="Top Contractors"
            role="contractor"
            data={contractors || []}
            onItemPress={handleContractorPress}
            viewAllRoute="/Contractors/ContractorList"
        />
    );
}
