import React, { useState, useEffect } from 'react';
import {
    Modal,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Image,
    StyleSheet,
    ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { showToast } from '../../../utils/showToast';

const ProfileModal = ({
    isVisible,
    onClose,
    userData,
    onSave,
    theme,
    isLoading,
}) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        location: [],
        avatar: null,
        imageData: null,
    });

    // Update formData whenever userData changes
    useEffect(() => {
        if (userData) {
            setFormData({
                name: userData.name || '',
                email: userData.email || '',
                phone: userData.phone || '',
                location: userData.location || [],
                avatar: userData.avatar || userData.avatarUri || null, // Support both avatar and avatarUri
                imageData: null, // Reset imageData when userData changes
            });
        }
    }, [userData]);

    const isFormValid = () => {
        return (
            formData.name.trim() &&
            (!formData.email ||
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))
        );
    };

    const handleInputChange = (field, value) => {
        setFormData({ ...formData, [field]: value });
    };

    const handleSave = () => {
        if (!isFormValid()) {
            showToast('error', 'Error', 'Please enter a valid name and email');
            return;
        }
        onSave(formData);
        onClose();
    };

    const handleClose = () => {
        // Reset form data to original userData when closing
        if (userData) {
            setFormData({
                name: userData.name || '',
                email: userData.email || '',
                phone: userData.phone || '',
                location: userData.location || [],
                avatar: userData.avatar || userData.avatarUri || null, // Support both avatar and avatarUri
                imageData: null, // Reset imageData when closing
            });
        }
        onClose();
    };

    const handleImagePick = async () => {
        try {
            const permission =
                await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (!permission.granted) {
                showToast(
                    'error',
                    'Error',
                    'Permission to access photos required'
                );
                return;
            }
            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images'],
                allowsEditing: true,
                aspect: [1, 1],
                quality: 1,
            });
            if (!result.canceled) {
                const selectedImage = result.assets[0];
                // Determine file type and extension
                const fileExtension =
                    selectedImage.uri.split('.').pop()?.toLowerCase() || 'jpg';
                const mimeType =
                    fileExtension === 'png' ? 'image/png' : 'image/jpeg';
                const fileName = `profile_${Date.now()}.${fileExtension}`;

                // Create image object matching backend expectations
                const imageData = {
                    uri: selectedImage.uri,
                    path: selectedImage.uri, // Backend expects 'path' property
                    type: mimeType,
                    name: fileName, // For FormData
                    filename: fileName, // Backend expects 'filename' property
                };
                setFormData({
                    ...formData,
                    avatar: selectedImage.uri,
                    imageData,
                });
            }
        } catch (error) {
            showToast('error', 'Error', 'Failed to pick image');
        }
    };

    // Don't render modal if userData is not available
    if (!userData || !isVisible) {
        return null;
    }

    return (
        <Modal
            visible={isVisible}
            animationType="fade"
            transparent
            onRequestClose={handleClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity
                    style={styles.overlayTouchable}
                    onPress={handleClose}
                    accessibilityLabel="Close Profile Modal"
                />
                <View
                    style={[
                        styles.modalContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={handleClose}
                        accessibilityLabel="Close profile modal"
                        accessibilityRole="button"
                        testID="close-modal"
                    >
                        <Ionicons
                            name="close"
                            size={24}
                            color={theme.PRIMARY}
                        />
                    </TouchableOpacity>

                    <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                        Profile Details
                    </Text>

                    <View style={styles.avatarContainer}>
                        <TouchableOpacity
                            style={[
                                styles.avatarWrapper,
                                { backgroundColor: theme.PRIMARY },
                            ]}
                            onPress={handleImagePick}
                        >
                            {formData.avatar ? (
                                <Image
                                    source={{ uri: formData.avatar }}
                                    style={styles.avatar}
                                    accessibilityLabel={`Profile picture for ${formData.name}`}
                                />
                            ) : (
                                <Text
                                    style={[
                                        styles.avatarText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    {formData.name
                                        ? formData.name.charAt(0).toUpperCase()
                                        : 'U'}
                                </Text>
                            )}
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.cameraButton,
                                {
                                    backgroundColor: theme.PRIMARY + '66',
                                },
                            ]}
                            onPress={handleImagePick}
                            accessibilityLabel="Change Profile Picture"
                            accessibilityRole="button"
                            testID="change-avatar"
                        >
                            <Ionicons
                                name="camera-outline"
                                size={20}
                                color={theme.WHITE}
                            />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.formContainer}>
                        <View style={styles.inputWrapper}>
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Name
                            </Text>
                            <TextInput
                                style={[
                                    styles.input,
                                    {
                                        borderColor: theme.INPUT_BORDER,
                                        color: theme.TEXT_PRIMARY,
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                    },
                                ]}
                                value={formData.name}
                                onChangeText={(text) =>
                                    handleInputChange('name', text)
                                }
                                placeholder="Enter name"
                                placeholderTextColor={
                                    theme.TEXT_SECONDARY + '80'
                                }
                                accessibilityLabel="Name input"
                            />
                        </View>

                        <View style={styles.inputWrapper}>
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Email
                            </Text>
                            <TextInput
                                style={[
                                    styles.input,
                                    {
                                        borderColor: theme.INPUT_BORDER,
                                        color: theme.TEXT_PRIMARY,
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                    },
                                ]}
                                value={formData.email}
                                onChangeText={(text) =>
                                    handleInputChange('email', text)
                                }
                                placeholder="Enter email"
                                placeholderTextColor={
                                    theme.TEXT_SECONDARY + '80'
                                }
                                keyboardType="email-address"
                                accessibilityLabel="Email input"
                            />
                        </View>

                        <View style={styles.inputWrapper}>
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Phone
                            </Text>
                            <TextInput
                                style={[
                                    styles.input,
                                    {
                                        borderColor: theme.INPUT_BORDER,
                                        color: theme.TEXT_PRIMARY,
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                    },
                                ]}
                                value={formData.phone}
                                onChangeText={(text) =>
                                    handleInputChange('phone', text)
                                }
                                placeholder="Enter phone number"
                                placeholderTextColor={
                                    theme.TEXT_SECONDARY + '80'
                                }
                                keyboardType="phone-pad"
                                accessibilityLabel="Phone input"
                            />
                        </View>
                        <View style={styles.inputWrapper}>
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Location
                            </Text>
                            <TextInput
                                style={[
                                    styles.input,
                                    {
                                        borderColor: theme.INPUT_BORDER,
                                        color: theme.TEXT_PRIMARY,
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                    },
                                ]}
                                value={formData.location.join(', ')}
                                onChangeText={(text) =>
                                    handleInputChange(
                                        'location',
                                        text.split(', ')
                                    )
                                }
                                placeholder="Enter location"
                                placeholderTextColor={
                                    theme.TEXT_SECONDARY + '80'
                                }
                                accessibilityLabel="Location input"
                            />
                        </View>
                    </View>

                    <TouchableOpacity
                        style={[
                            styles.saveButton,
                            {
                                backgroundColor: theme.PRIMARY,
                                opacity: isLoading || !isFormValid() ? 0.6 : 1,
                            },
                        ]}
                        onPress={handleSave}
                        disabled={isLoading || !isFormValid()}
                        accessibilityLabel="Save Profile Changes"
                        accessibilityRole="button"
                        testID="save-profile"
                    >
                        {isLoading ? (
                            <ActivityIndicator
                                size="small"
                                color={theme.WHITE}
                            />
                        ) : (
                            <Text
                                style={[
                                    styles.saveButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Save Changes
                            </Text>
                        )}
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    overlayTouchable: {
        position: 'absolute',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
    },
    modalContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 16,
        padding: 24,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 6,
    },
    closeButton: {
        position: 'absolute',
        top: 16,
        right: 16,
        padding: 8,
        zIndex: 1,
    },
    title: {
        fontSize: 22,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 20,
    },
    avatarContainer: {
        alignItems: 'center',
        marginBottom: 20,
        position: 'relative',
    },
    avatarWrapper: {
        width: 90,
        height: 90,
        borderRadius: 45,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
    },
    avatar: {
        width: '100%',
        height: '100%',
    },
    avatarText: {
        fontSize: 36,
        fontWeight: 'bold',
    },
    cameraButton: {
        position: 'absolute',
        // top: 0,
        bottom: 0,
        // left: 50,
        right: 90,
        padding: 8,
        borderRadius: 30,
    },
    formContainer: {
        gap: 16,
    },
    inputWrapper: {
        gap: 8,
    },
    label: {
        fontSize: 16,
        fontWeight: '500',
    },
    input: {
        borderWidth: 1,
        borderRadius: 12,
        padding: 12,
        fontSize: 16,
    },
    saveButton: {
        marginTop: 20,
        padding: 14,
        borderRadius: 12,
        alignItems: 'center',
    },
    saveButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
});

export default ProfileModal;
