// BrokerApplyIndex.jsx

import React, { useState, useRef, memo, useContext } from 'react';
import {
    ScrollView,
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import BackButton from '../Components/Shared/BackButton';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const BrokerApplyIndex = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [agreed, setAgreed] = useState(false);
    const buttonScale = useRef(new Animated.Value(1)).current;

    const toggleAgreement = () => setAgreed((prev) => !prev);

    // Handle continue button
    const handleContinue = () => {
        if (!agreed) {
            showToast(
                'error',
                'Agreement Required',
                'Please agree to the terms and conditions to proceed.'
            );
            return;
        }
        router.push('/Broker/BrokerForm');
    };

    const handlePressIn = () => {
        Animated.spring(buttonScale, {
            toValue: 0.95,
            friction: 8,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(buttonScale, {
            toValue: 1,
            friction: 8,
            useNativeDriver: true,
        }).start();
    };

    return (
        <View style={styles.container}>
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                start={{ x: 0, y: 0.2 }}
                end={{ x: 0.9, y: 0.4 }}
                style={styles.background}
            />
            <BackButton color={theme.WHITE} />
            <ScrollView contentContainerStyle={styles.scrollContent}>
                <View
                    style={[
                        styles.termsCard,
                        {
                            shadowColor: theme.PRIMARY,
                            backgroundColor: theme.CARD,
                        },
                    ]}
                >
                    <Text style={[styles.termsTitle, { color: theme.PRIMARY }]}>
                        Terms and Conditions
                    </Text>
                    <Text style={[styles.termsText, { color: theme.PRIMARY }]}>
                        By applying to become a Site Scout on Build Connect, you
                        agree to the following terms:
                    </Text>

                    <Text
                        style={[styles.sectionTitle, { color: theme.PRIMARY }]}
                    >
                        1. Eligibility
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • You must be at least 18 years old and legally eligible
                        to work in India.
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • You are required to provide valid identification
                        (Aadhaar, PAN) and accurate contact information.
                    </Text>

                    <Text
                        style={[styles.sectionTitle, { color: theme.PRIMARY }]}
                    >
                        2. Responsibilities
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Conduct property verifications honestly and submit
                        truthful reports.
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Assist clients in navigating property-related queries
                        on Build Connect.
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Uphold the highest standards of professional behavior
                        and integrity.
                    </Text>

                    <Text
                        style={[styles.sectionTitle, { color: theme.PRIMARY }]}
                    >
                        3. Commission & Payout
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • You will receive a commission for each verified and
                        closed deal, as per Build Connect&apos;s payout
                        structure.
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Payouts are processed within 7-14 business days after
                        transaction completion.
                    </Text>

                    <Text
                        style={[styles.sectionTitle, { color: theme.PRIMARY }]}
                    >
                        4. Verification Process
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Your application will undergo background and
                        credential verification which may take up to 7 business
                        days.
                    </Text>

                    <Text
                        style={[styles.sectionTitle, { color: theme.PRIMARY }]}
                    >
                        5. Code of Conduct
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Brokers must follow fair and transparent dealings with
                        clients.
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Misrepresentation, fraud, or client harassment will
                        result in immediate termination.
                    </Text>

                    <Text
                        style={[styles.sectionTitle, { color: theme.PRIMARY }]}
                    >
                        6. Confidentiality
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • You are responsible for safeguarding client
                        information and not sharing it with third parties.
                    </Text>

                    <Text
                        style={[styles.sectionTitle, { color: theme.PRIMARY }]}
                    >
                        7. Termination
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • Build Connect reserves the right to terminate your
                        account for policy violations or client complaints.
                    </Text>
                    <Text
                        style={[
                            styles.termsItem,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        • You may voluntarily deactivate your account after
                        resolving pending transactions.
                    </Text>

                    <TouchableOpacity onPress={toggleAgreement}>
                        <View style={styles.checkboxContainer}>
                            <Ionicons
                                name={agreed ? 'checkbox' : 'square-outline'}
                                size={24}
                                color={
                                    agreed ? theme.PRIMARY : theme.GRAY_LIGHT
                                }
                            />
                            <Text
                                style={[
                                    styles.checkboxLabel,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                I have read and agree to the terms and
                                conditions
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>

                <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
                    <TouchableOpacity
                        style={styles.continueButton}
                        onPress={handleContinue}
                        onPressIn={handlePressIn}
                        onPressOut={handlePressOut}
                    >
                        <LinearGradient
                            colors={[theme.PRIMARY, theme.SECONDARY]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                            style={styles.buttonGradient}
                        >
                            <Text
                                style={[
                                    styles.buttonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Continue
                            </Text>
                            <Ionicons
                                name="arrow-forward"
                                size={18}
                                color={theme.WHITE}
                                style={styles.buttonIcon}
                            />
                        </LinearGradient>
                    </TouchableOpacity>
                </Animated.View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1 },
    background: { ...StyleSheet.absoluteFillObject, zIndex: -1 },
    scrollContent: { paddingVertical: 12, paddingHorizontal: 12 },
    termsCard: {
        borderRadius: 20,
        padding: 24,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
        marginBottom: 24,
    },
    termsTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        marginBottom: 16,
        textAlign: 'center',
    },
    termsText: { fontSize: 16, marginBottom: 12 },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 16,
        marginBottom: 8,
    },
    termsItem: {
        textAlign: 'justify',
        lineHeight: 20,
        fontSize: 15,
        marginBottom: 8,
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 16,
    },
    checkboxLabel: {
        fontSize: 15,
        flex: 1,
        textAlign: 'justify',
        marginLeft: 12,
    },
    continueButton: { alignSelf: 'center' },
    buttonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 14,
        paddingHorizontal: 24,
        paddingVertical: 12,
    },
    buttonText: { fontSize: 16, fontWeight: '600' },
    buttonIcon: { marginLeft: 8 },
});

export default memo(BrokerApplyIndex);
