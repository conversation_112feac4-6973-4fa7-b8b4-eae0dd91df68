import { privateAPIClient } from '../index';

export const fetchContractorProfile = async () => {
    const url = '/user-service/api/v1/contractors';
    const response = await privateAPIClient.get(url);
    return response.data.application;
};

// Fetch all contractors for home page cards
export const fetchAllContractors = async () => {
    const url = '/user-service/api/v1/contractors/all';
    const response = await privateAPIClient.get(url);
    return response.data.contractors;
};

// Fetch individual contractor profile by ID
export const fetchContractorProfileById = async (contractorId) => {
    const url = `/user-service/api/v1/contractors/profile/${contractorId}`;
    const response = await privateAPIClient.get(url);
    return response.data.contractor;
};

export const createContractorApplication = async (data) => {
    const response = await privateAPIClient.post(
        '/user-service/api/v1/contractors',
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

export const updateContractorProfile = async (id, data) => {
    const response = await privateAPIClient.patch(
        `/user-service/api/v1/contractors/${id}`,
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

export const deleteContractorApplication = async (id) => {
    const response = await privateAPIClient.delete(
        `/user-service/api/v1/contractors/${id}`
    );
    return response.data;
};

// Portfolio management functions
export const addPortfolioItem = async (data) => {
    const response = await privateAPIClient.post(
        '/user-service/api/v1/contractors/portfolio',
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

export const updatePortfolioItem = async (portfolioItemId, data) => {
    const response = await privateAPIClient.patch(
        `/user-service/api/v1/contractors/portfolio/${portfolioItemId}`,
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

export const deletePortfolioItem = async (portfolioItemId) => {
    const response = await privateAPIClient.delete(
        `/user-service/api/v1/contractors/portfolio/${portfolioItemId}`
    );
    return response.data;
};
