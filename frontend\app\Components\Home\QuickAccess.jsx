import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import React, { useContext } from 'react';
import {
    MaterialIcons,
    Ionicons,
    MaterialCommunityIcons,
} from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';

const values = [
    {
        id: 1,
        name: 'Sites',
        icon: 'home-map-marker',
        iconType: 'MaterialCommunityIcons',
        route: '/Properties/LandList',
    },
    {
        id: 2,
        name: 'Contractors',
        icon: 'engineering',
        iconType: 'MaterialIcons',
        route: '/Contractors/ContractorList',
    },
    {
        id: 3,
        name: 'Site Scouts',
        icon: 'person-search',
        iconType: 'MaterialIcons',
        route: '/Broker/BrokerList',
    },
    {
        id: 4,
        name: 'Map Explorer',
        icon: 'map',
        iconType: 'Ionicons',
        route: '/Map/MapExplorer',
    },
    {
        id: 5,
        name: 'Chats',
        icon: 'chatbubbles-outline',
        iconType: 'Ionicons',
        route: '/(tabs)/Chats',
    },
    {
        id: 6,
        name: 'Support',
        icon: 'support-agent',
        iconType: 'MaterialIcons',
        route: '/Support/HelpSupport',
    },
    {
        id: 7,
        name: 'Payments',
        icon: 'card-outline',
        iconType: 'Ionicons',
        route: '/Payment/WalletScreen',
    },
];

export default function QuickAccess() {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();

    const renderIcon = (icon, iconType) => {
        if (iconType === 'MaterialIcons') {
            return (
                <MaterialIcons name={icon} size={18} color={theme.PRIMARY} />
            );
        }
        if (iconType === 'MaterialCommunityIcons') {
            return (
                <MaterialCommunityIcons
                    name={icon}
                    size={18}
                    color={theme.PRIMARY}
                />
            );
        }
        return <Ionicons name={icon} size={18} color={theme.PRIMARY} />;
    };

    return (
        <View
            style={{
                borderRadius: 10,
                paddingVertical: 2,
                marginVertical: 2,
            }}
        >
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                    paddingHorizontal: -4,
                }}
            >
                {values.map((item) => (
                    <TouchableOpacity
                        key={item.id}
                        style={{ maxWidth: '35%', marginHorizontal: 4 }}
                        onPress={() => router.push(item.route)}
                        activeOpacity={0.8}
                    >
                        <View
                            style={{
                                borderRadius: 60,
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignItems: 'center',
                                paddingLeft: 12,
                                paddingRight: 12,
                                paddingVertical: 4,
                                borderColor: theme.GRAY_LIGHT,
                                borderWidth: 1,
                                width: 'auto',
                            }}
                        >
                            {renderIcon(item.icon, item.iconType)}
                            <Text
                                style={{
                                    color: theme.TEXT_SECONDARY,
                                    fontSize: 10,
                                    fontWeight: '500',
                                    textAlign: 'center',
                                    marginLeft: 2,
                                }}
                            >
                                {item.name}
                            </Text>
                        </View>
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </View>
    );
}
