import { View, TouchableOpacity, Image } from 'react-native';
import React, { useState, useContext } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import OptionModal from './OptionModal';
import { ThemeContext } from '../../../context/ThemeContext';
import { router } from 'expo-router';

export default function Header() {
    const { theme } = useContext(ThemeContext);
    const [OptionModalVisible, setOptionModalVisible] = useState(false);
    return (
        <View
            style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingHorizontal: 12,
                paddingTop: 2,
                paddingBottom: 2,
                zIndex: 1,
            }}
        >
            {/* <LinearGradient
                                colors={[theme.GRADIENT_PRIMARY + '80', theme.GRADIENT_SECONDARY +'80', theme.BACKGROUND + '80']}
                                style={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                }}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 0, y: 1 }}
                            /> */}
            <OptionModal
                OptionModalVisible={OptionModalVisible}
                setOptionModalVisible={setOptionModalVisible}
            />
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    flex: 1,
                    justifyContent: 'left',
                }}
            >
                <Image
                    source={require('../../../assets/images/logo.png')}
                    style={{
                        height: 40,
                        width: 40,
                        borderRadius: 40,
                    }}
                />
            </View>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                }}
            >
                <TouchableOpacity style={styles.iconButton}>
                    <Ionicons
                        name="search"
                        size={24}
                        color={theme.PRIMARY}
                        style={{ padding: 8 }}
                    />
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() =>
                        router.push('/Notifications/NotificationsList')
                    }
                    style={styles.iconButton}
                >
                    <Ionicons
                        name="notifications"
                        size={24}
                        color={theme.PRIMARY}
                        style={{ padding: 8 }}
                    />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.iconButton}
                    onPress={() => setOptionModalVisible(true)}
                >
                    <Ionicons
                        name="ellipsis-vertical"
                        size={24}
                        color={theme.PRIMARY}
                        style={{ padding: 8 }}
                    />
                </TouchableOpacity>
            </View>
        </View>
    );
}

const styles = {
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 20,
    },
    logoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    logo: {
        height: 40,
        width: 40,
        borderRadius: 40,
        borderWidth: 0.5,
    },
    actionsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconButton: {
        borderRadius: 30,
    },
};
